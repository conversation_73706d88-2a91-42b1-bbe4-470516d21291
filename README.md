# UPI Transaction App

A comprehensive UPI-based transaction application with multi-layer security including user authentication, OTP verification, and face recognition for high-value transactions.

## 🚀 Features

- **🔐 User Authentication**: JWT-based secure authentication system
- **📱 OTP Verification**: SMS/Email OTP for transaction confirmation
- **👤 Face Recognition**: Real-time face verification for transactions > ₹20,000
- **🔄 Version-based APIs**: RESTful APIs with version control
- **🛡️ Secure Transactions**: End-to-end encrypted transaction processing
- **📊 Transaction History**: Complete transaction tracking and analytics
- **⚡ Real-time Updates**: Live transaction status updates
- **🎨 Modern UI**: Responsive Material-UI design

## 🛠️ Tech Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and ORM
- **PostgreSQL**: Primary database
- **Redis**: Caching and session management
- **OpenCV**: Computer vision for face recognition
- **JWT**: Token-based authentication
- **Twilio**: SMS OTP delivery
- **SendGrid**: Email notifications

### Frontend
- **React 18**: Modern UI library with hooks
- **Vite**: Fast build tool and dev server
- **Material-UI v5**: Component library
- **React Router v6**: Client-side routing
- **Zustand**: State management
- **React Query**: Server state management
- **React Webcam**: Camera integration
- **Framer Motion**: Animations

## 📁 Project Structure

```
transaction/
├── backend/                    # FastAPI Backend
│   ├── app/
│   │   ├── api/v1/            # API Version 1
│   │   │   ├── endpoints/     # API endpoints
│   │   │   └── api.py         # Router configuration
│   │   ├── core/              # Core functionality
│   │   │   ├── config.py      # Configuration
│   │   │   ├── database.py    # Database setup
│   │   │   ├── security.py    # Security utilities
│   │   │   ├── dependencies.py # FastAPI dependencies
│   │   │   └── middleware.py  # Security middleware
│   │   ├── models/            # Database models
│   │   │   ├── user.py        # User model
│   │   │   ├── transaction.py # Transaction model
│   │   │   ├── otp.py         # OTP model
│   │   │   ├── face_recognition.py # Face recognition model
│   │   │   └── schemas.py     # Pydantic schemas
│   │   ├── services/          # Business logic
│   │   │   ├── auth_service.py
│   │   │   ├── transaction_service.py
│   │   │   ├── otp_service.py
│   │   │   ├── face_recognition_service.py
│   │   │   └── notification_service.py
│   │   ├── utils/             # Utilities
│   │   │   └── validators.py  # Input validation
│   │   └── main.py            # FastAPI app
│   ├── requirements.txt       # Python dependencies
│   ├── Dockerfile            # Docker configuration
│   └── .env.example          # Environment template
├── frontend/                  # React Frontend
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   │   ├── auth/         # Authentication components
│   │   │   ├── layout/       # Layout components
│   │   │   └── transaction/  # Transaction components
│   │   ├── pages/            # Page components
│   │   │   ├── auth/         # Auth pages
│   │   │   ├── DashboardPage.jsx
│   │   │   ├── TransactionPage.jsx
│   │   │   ├── TransactionHistoryPage.jsx
│   │   │   ├── ProfilePage.jsx
│   │   │   └── FaceEnrollmentPage.jsx
│   │   ├── services/         # API services
│   │   │   └── api.js        # API client
│   │   ├── store/            # State management
│   │   │   └── authStore.js  # Auth store
│   │   ├── App.jsx           # Main app component
│   │   └── main.jsx          # Entry point
│   ├── package.json          # Node dependencies
│   ├── vite.config.js        # Vite configuration
│   ├── Dockerfile           # Docker configuration
│   └── .env.example         # Environment template
├── docker-compose.yml        # Docker Compose setup
└── README.md                # This file
```

## 🔒 Security Features

### Multi-Layer Security
1. **JWT Authentication**: Secure token-based authentication
2. **OTP Verification**: SMS/Email OTP for all transactions
3. **Face Recognition**: Biometric verification for high-value transactions (>₹20k)
4. **Rate Limiting**: API rate limiting to prevent abuse
5. **Input Validation**: Comprehensive input sanitization
6. **SQL Injection Protection**: Middleware to detect and block SQL injection attempts
7. **CSRF Protection**: Cross-site request forgery protection
8. **Security Headers**: Comprehensive security headers
9. **Request Size Limiting**: Protection against large payload attacks

### Transaction Security Levels
- **Standard (≤₹20,000)**: User Authentication + OTP
- **High-Value (>₹20,000)**: User Authentication + OTP + Face Recognition

## 🚀 Quick Start

### Using Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd transaction
   ```

2. **Set up environment variables**
   ```bash
   # Backend
   cp backend/.env.example backend/.env

   # Frontend
   cp frontend/.env.example frontend/.env
   ```

3. **Start the application**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/api/v1/docs

### Manual Setup

#### Backend Setup

1. **Install Python dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Set up PostgreSQL and Redis**
   ```bash
   # Install PostgreSQL and Redis
   # Create database: upi_transaction_db
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database and service credentials
   ```

4. **Run the backend**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

#### Frontend Setup

1. **Install Node.js dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API URL
   ```

3. **Run the frontend**
   ```bash
   npm run dev
   ```

## 📋 Environment Configuration

### Backend (.env)
```env
# Application
APP_NAME=UPI Transaction App
VERSION=1.0.0
DEBUG=true

# Database
DATABASE_URL=postgresql://upi_user:upi_password@localhost:5432/upi_transaction_db

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Transaction Limits
HIGH_VALUE_TRANSACTION_LIMIT=20000.0
DAILY_TRANSACTION_LIMIT=100000.0

# Twilio (SMS OTP)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# SendGrid (Email)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Face Recognition
FACE_RECOGNITION_TOLERANCE=0.6
FACE_RECOGNITION_MODEL=hog
```

### Frontend (.env)
```env
VITE_API_URL=http://localhost:8000/api/v1
VITE_APP_NAME=UPI Transaction App
VITE_APP_VERSION=1.0.0
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/auth/refresh` - Refresh token

### Transaction Endpoints
- `POST /api/v1/transactions/create` - Create transaction
- `POST /api/v1/transactions/{id}/process` - Process transaction
- `GET /api/v1/transactions/history` - Transaction history
- `GET /api/v1/transactions/{id}` - Get transaction details
- `POST /api/v1/transactions/{id}/cancel` - Cancel transaction

### OTP Endpoints
- `POST /api/v1/otp/send` - Send OTP
- `POST /api/v1/otp/verify` - Verify OTP
- `GET /api/v1/otp/status` - Get OTP status

### Face Recognition Endpoints
- `POST /api/v1/face-recognition/enroll` - Enroll face
- `POST /api/v1/face-recognition/verify` - Verify face
- `GET /api/v1/face-recognition/status` - Get enrollment status
- `GET /api/v1/face-recognition/templates` - List face templates

### Health Endpoints
- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/detailed` - Detailed health check
- `GET /api/v1/info` - API information

## 🔄 Transaction Flow

1. **User Authentication**: Login with email/password
2. **Create Transaction**: Enter recipient UPI ID and amount
3. **OTP Verification**: Verify transaction with SMS/Email OTP
4. **Face Verification** (if amount > ₹20k): Capture and verify face
5. **Process Transaction**: Complete the UPI transaction
6. **Notification**: Send confirmation to both parties

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest tests/ -v
```

### Frontend Testing
```bash
cd frontend
npm test
```

## 📊 Monitoring and Logging

- **Request Logging**: All API requests are logged
- **Error Tracking**: Comprehensive error logging
- **Performance Monitoring**: Request timing and performance metrics
- **Security Monitoring**: Failed authentication attempts and suspicious activity

## 🔧 Development

### Adding New Features
1. Create feature branch
2. Implement backend changes in `backend/app/`
3. Add corresponding frontend components in `frontend/src/`
4. Update API documentation
5. Add tests
6. Submit pull request

### Database Migrations
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

## 🚀 Deployment

### Production Considerations
1. **Environment Variables**: Set production values
2. **Database**: Use managed PostgreSQL service
3. **Redis**: Use managed Redis service
4. **SSL/TLS**: Enable HTTPS
5. **Load Balancing**: Use load balancer for high availability
6. **Monitoring**: Set up application monitoring
7. **Backup**: Regular database backups

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/api/v1/docs`
- Review the troubleshooting section below

## 🔧 Troubleshooting

### Common Issues

1. **Camera not working**: Ensure HTTPS or localhost for camera access
2. **Database connection failed**: Check PostgreSQL service and credentials
3. **Redis connection failed**: Ensure Redis is running
4. **Face recognition errors**: Install required system dependencies for OpenCV
5. **SMS/Email not working**: Configure Twilio/SendGrid credentials

### System Requirements
- **Backend**: Python 3.9+, 2GB RAM, OpenCV dependencies
- **Frontend**: Node.js 16+, Modern browser with camera support
- **Database**: PostgreSQL 12+, Redis 6+
