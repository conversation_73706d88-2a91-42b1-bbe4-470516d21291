# UPI Transaction App

A comprehensive UPI-based transaction application with multi-layer security including user authentication, OTP verification, and face recognition for high-value transactions.

## Features

- **User Authentication**: JWT-based secure authentication system
- **OTP Verification**: SMS/Email OTP for transaction confirmation
- **Face Recognition**: Real-time face verification for transactions > ₹20,000
- **Version-based APIs**: RESTful APIs with version control
- **Secure Transactions**: End-to-end encrypted transaction processing

## Tech Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and ORM
- **PostgreSQL**: Primary database
- **Redis**: Caching and session management
- **OpenCV**: Computer vision for face recognition
- **JWT**: Token-based authentication

### Frontend
- **React**: Modern UI library
- **Vite**: Fast build tool
- **Axios**: HTTP client
- **React Router**: Client-side routing
- **Material-UI**: Component library

## Project Structure

```
transaction/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   │   ├── v1/
│   │   │   │   ├── endpoints/
│   │   │   │   └── __init__.py
│   │   │   └── __init__.py
│   │   ├── core/
│   │   ├── models/
│   │   ├── services/
│   │   ├── utils/
│   │   └── main.py
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   ├── utils/
│   │   └── App.jsx
│   ├── package.json
│   └── Dockerfile
├── docker-compose.yml
└── README.md
```

## Security Features

1. **Multi-factor Authentication**
2. **Transaction Amount-based Security Levels**
3. **Real-time Face Recognition**
4. **OTP Verification**
5. **JWT Token Management**
6. **Input Validation and Sanitization**

## Getting Started

### Prerequisites
- Python 3.9+
- Node.js 16+
- PostgreSQL
- Redis

### Installation
1. Clone the repository
2. Set up backend dependencies
3. Set up frontend dependencies
4. Configure environment variables
5. Run the application

## API Versioning

The API follows semantic versioning with the following structure:
- `/api/v1/` - Current stable version
- Future versions will be `/api/v2/`, `/api/v3/`, etc.

## License

MIT License
