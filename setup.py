#!/usr/bin/env python3
"""
Setup script for UPI Transaction App
This script creates virtual environments and installs dependencies
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None, shell=True):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=shell, 
            check=True, 
            capture_output=True, 
            text=True,
            cwd=cwd
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def check_python_version():
    """Check if Python version is 3.9 or higher"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_node_version():
    """Check if Node.js version is 16 or higher"""
    success, output = run_command("node --version")
    if not success:
        print("❌ Node.js is not installed")
        return False
    
    version_str = output.strip().replace('v', '')
    major_version = int(version_str.split('.')[0])
    
    if major_version < 16:
        print(f"❌ Node.js 16 or higher is required. Current: {version_str}")
        return False
    
    print(f"✅ Node.js version: {version_str}")
    return True

def create_backend_venv():
    """Create virtual environment for backend"""
    print("\n🔧 Setting up Backend Virtual Environment...")
    
    backend_dir = Path("backend")
    venv_dir = backend_dir / "venv"
    
    # Create virtual environment
    print("Creating virtual environment...")
    success, output = run_command(f"python -m venv {venv_dir}")
    if not success:
        print(f"❌ Failed to create virtual environment: {output}")
        return False
    
    # Determine activation script path based on OS
    if platform.system() == "Windows":
        activate_script = venv_dir / "Scripts" / "activate.bat"
        pip_path = venv_dir / "Scripts" / "pip.exe"
    else:
        activate_script = venv_dir / "bin" / "activate"
        pip_path = venv_dir / "bin" / "pip"
    
    print(f"✅ Virtual environment created at: {venv_dir}")
    
    # Upgrade pip
    print("Upgrading pip...")
    success, output = run_command(f"{pip_path} install --upgrade pip")
    if not success:
        print(f"⚠️ Warning: Failed to upgrade pip: {output}")
    
    # Install requirements
    requirements_file = backend_dir / "requirements.txt"
    if requirements_file.exists():
        print("Installing Python dependencies...")
        success, output = run_command(f"{pip_path} install -r {requirements_file}")
        if not success:
            print(f"❌ Failed to install requirements: {output}")
            return False
        print("✅ Python dependencies installed successfully")
    else:
        print("⚠️ Warning: requirements.txt not found")
    
    return True, activate_script

def setup_frontend():
    """Setup frontend dependencies"""
    print("\n🔧 Setting up Frontend Dependencies...")
    
    frontend_dir = Path("frontend")
    
    # Check if package.json exists
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json not found in frontend directory")
        return False
    
    # Install npm dependencies
    print("Installing Node.js dependencies...")
    success, output = run_command("npm install", cwd=frontend_dir)
    if not success:
        print(f"❌ Failed to install npm dependencies: {output}")
        return False
    
    print("✅ Node.js dependencies installed successfully")
    return True

def create_env_files():
    """Create environment files from examples"""
    print("\n🔧 Setting up Environment Files...")
    
    # Backend .env
    backend_env_example = Path("backend/.env.example")
    backend_env = Path("backend/.env")
    
    if backend_env_example.exists() and not backend_env.exists():
        backend_env.write_text(backend_env_example.read_text())
        print("✅ Created backend/.env from example")
    
    # Frontend .env
    frontend_env_example = Path("frontend/.env.example")
    frontend_env = Path("frontend/.env")
    
    if frontend_env_example.exists() and not frontend_env.exists():
        frontend_env.write_text(frontend_env_example.read_text())
        print("✅ Created frontend/.env from example")

def create_activation_scripts():
    """Create convenient activation scripts"""
    print("\n🔧 Creating Activation Scripts...")
    
    # Windows activation script
    if platform.system() == "Windows":
        activate_script = """@echo off
echo Activating UPI Transaction App Backend Environment...
call backend\\venv\\Scripts\\activate.bat
echo ✅ Backend virtual environment activated!
echo.
echo Available commands:
echo   cd backend ^&^& uvicorn app.main:app --reload  (Start backend server)
echo   cd frontend ^&^& npm run dev                    (Start frontend server)
echo   deactivate                                     (Deactivate environment)
echo.
cmd /k
"""
        with open("activate.bat", "w") as f:
            f.write(activate_script)
        print("✅ Created activate.bat for Windows")
    
    # Unix/Linux/Mac activation script
    activate_script = """#!/bin/bash
echo "Activating UPI Transaction App Backend Environment..."
source backend/venv/bin/activate
echo "✅ Backend virtual environment activated!"
echo ""
echo "Available commands:"
echo "  cd backend && uvicorn app.main:app --reload  # Start backend server"
echo "  cd frontend && npm run dev                    # Start frontend server"
echo "  deactivate                                    # Deactivate environment"
echo ""
exec "$SHELL"
"""
    with open("activate.sh", "w") as f:
        f.write(activate_script)
    
    # Make script executable on Unix systems
    if platform.system() != "Windows":
        os.chmod("activate.sh", 0o755)
        print("✅ Created activate.sh for Unix/Linux/Mac")

def print_next_steps(backend_activate_script):
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("1. Configure your environment variables:")
    print("   - Edit backend/.env with your database and service credentials")
    print("   - Edit frontend/.env if needed")
    
    print("\n2. Set up your databases:")
    print("   - Install and start PostgreSQL")
    print("   - Install and start Redis")
    print("   - Create database: upi_transaction_db")
    
    print("\n3. Start the application:")
    
    if platform.system() == "Windows":
        print("   Backend:")
        print("     activate.bat")
        print("     cd backend")
        print("     uvicorn app.main:app --reload")
        print("")
        print("   Frontend (in new terminal):")
        print("     cd frontend")
        print("     npm run dev")
    else:
        print("   Backend:")
        print("     ./activate.sh")
        print("     cd backend")
        print("     uvicorn app.main:app --reload")
        print("")
        print("   Frontend (in new terminal):")
        print("     cd frontend")
        print("     npm run dev")
    
    print("\n4. Access the application:")
    print("   - Frontend: http://localhost:3000")
    print("   - Backend API: http://localhost:8000")
    print("   - API Docs: http://localhost:8000/api/v1/docs")
    
    print("\n🐳 Alternative: Use Docker")
    print("   docker-compose up -d")
    
    print("\n" + "="*60)

def main():
    """Main setup function"""
    print("🚀 UPI Transaction App Setup")
    print("="*40)
    
    # Check prerequisites
    print("Checking prerequisites...")
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        print("Please install Node.js 16+ from https://nodejs.org/")
        sys.exit(1)
    
    # Setup backend
    success, activate_script = create_backend_venv()
    if not success:
        print("❌ Backend setup failed")
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        print("❌ Frontend setup failed")
        sys.exit(1)
    
    # Create environment files
    create_env_files()
    
    # Create activation scripts
    create_activation_scripts()
    
    # Print next steps
    print_next_steps(activate_script)

if __name__ == "__main__":
    main()
