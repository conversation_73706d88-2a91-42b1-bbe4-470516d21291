# Application Configuration
APP_NAME=UPI Transaction App
VERSION=1.0.0
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql://upi_user:upi_password@localhost:5432/upi_transaction_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# OTP Configuration
OTP_EXPIRE_MINUTES=5
OTP_LENGTH=6

# Transaction Configuration
HIGH_VALUE_TRANSACTION_LIMIT=20000.0
DAILY_TRANSACTION_LIMIT=100000.0

# Twilio Configuration (for SMS OTP)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# SendGrid Configuration (for Email OTP)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Face Recognition Configuration
FACE_RECOGNITION_TOLERANCE=0.6
FACE_RECOGNITION_MODEL=hog

# CORS Configuration
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
