// Global state
let currentUser = null;
let authToken = null;
let currentTransactionId = null;

// API Configuration
const API_BASE = '/api/v1';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    // Check if user is logged in
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    
    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showAuthenticatedApp();
    } else {
        showPage('login');
    }
    
    // Hide loading screen
    setTimeout(() => {
        document.getElementById('loading').style.display = 'none';
    }, 1000);
    
    // Setup form handlers
    setupFormHandlers();
    setupOtpInputs();
}

function setupFormHandlers() {
    // Login form
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    
    // Register form
    document.getElementById('register-form').addEventListener('submit', handleRegister);
    
    // Transaction form
    document.getElementById('transaction-form').addEventListener('submit', handleTransaction);
    
    // OTP form
    document.getElementById('otp-form').addEventListener('submit', handleOtpVerification);
}

function setupOtpInputs() {
    const otpInputs = document.querySelectorAll('.otp-input');
    otpInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            if (e.target.value.length === 1 && index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }
        });
        
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && e.target.value === '' && index > 0) {
                otpInputs[index - 1].focus();
            }
        });
    });
}

// Authentication functions
async function handleLogin(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const credentials = Object.fromEntries(formData);
    
    try {
        showToast('Logging in...', 'info');
        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            authToken = data.access_token;
            currentUser = data.user;
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            showToast('Login successful!', 'success');
            showAuthenticatedApp();
        } else {
            showToast(data.detail || 'Login failed', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('Login error:', error);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const userData = Object.fromEntries(formData);
    
    try {
        showToast('Creating account...', 'info');
        const response = await fetch(`${API_BASE}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showToast('Account created successfully! Please login.', 'success');
            showPage('login');
        } else {
            showToast(data.detail || 'Registration failed', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('Registration error:', error);
    }
}

function logout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    showPage('login');
    showToast('Logged out successfully', 'info');
}

// Transaction functions
async function handleTransaction(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const transactionData = Object.fromEntries(formData);
    
    // Convert amount to number
    transactionData.amount = parseFloat(transactionData.amount);
    
    try {
        showToast('Creating transaction...', 'info');
        const response = await fetch(`${API_BASE}/transactions/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify(transactionData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentTransactionId = data.transaction_id;
            showToast('Transaction created. Sending OTP...', 'success');
            
            // Send OTP
            await sendOtp();
        } else {
            showToast(data.detail || 'Transaction failed', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('Transaction error:', error);
    }
}

async function sendOtp() {
    try {
        const response = await fetch(`${API_BASE}/otp/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                phone_number: currentUser.phone_number,
                otp_type: 'sms'
            })
        });
        
        if (response.ok) {
            showOtpModal();
        } else {
            showToast('Failed to send OTP', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('OTP error:', error);
    }
}

async function handleOtpVerification(e) {
    e.preventDefault();
    
    const otpInputs = document.querySelectorAll('.otp-input');
    const otpCode = Array.from(otpInputs).map(input => input.value).join('');
    
    if (otpCode.length !== 6) {
        showToast('Please enter complete OTP', 'warning');
        return;
    }
    
    try {
        showToast('Verifying OTP...', 'info');
        const response = await fetch(`${API_BASE}/otp/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                phone_number: currentUser.phone_number,
                otp_code: otpCode
            })
        });
        
        if (response.ok) {
            showToast('OTP verified! Processing transaction...', 'success');
            closeOtpModal();
            
            // Process the transaction
            await processTransaction();
        } else {
            showToast('Invalid OTP. Please try again.', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('OTP verification error:', error);
    }
}

async function processTransaction() {
    try {
        const response = await fetch(`${API_BASE}/transactions/${currentTransactionId}/process`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showToast('Transaction completed successfully!', 'success');
            document.getElementById('transaction-form').reset();
            showPage('dashboard');
            loadDashboardData();
        } else {
            showToast(data.detail || 'Transaction processing failed', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('Transaction processing error:', error);
    }
}

async function resendOtp() {
    await sendOtp();
    showToast('OTP resent successfully', 'info');
}

// Data loading functions
async function loadDashboardData() {
    try {
        // Load user profile
        const profileResponse = await fetch(`${API_BASE}/users/profile`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (profileResponse.ok) {
            const profile = await profileResponse.json();
            document.getElementById('user-name').textContent = profile.full_name;
            document.getElementById('account-balance').textContent = '10,000.00'; // Mock balance
        }
        
        // Load recent transactions
        await loadRecentTransactions();
        
    } catch (error) {
        console.error('Dashboard data loading error:', error);
    }
}

async function loadRecentTransactions() {
    try {
        const response = await fetch(`${API_BASE}/transactions/history?limit=5`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            displayRecentTransactions(data.transactions || []);
        }
    } catch (error) {
        console.error('Recent transactions loading error:', error);
    }
}

async function loadTransactionHistory() {
    try {
        showToast('Loading transaction history...', 'info');
        const statusFilter = document.getElementById('status-filter').value;
        const url = statusFilter ? 
            `${API_BASE}/transactions/history?status=${statusFilter}` : 
            `${API_BASE}/transactions/history`;
            
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            displayTransactionHistory(data.transactions || []);
        } else {
            showToast('Failed to load transaction history', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
        console.error('Transaction history loading error:', error);
    }
}

async function loadProfileData() {
    try {
        const response = await fetch(`${API_BASE}/users/profile`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const profile = await response.json();
            displayProfileInfo(profile);
        }
    } catch (error) {
        console.error('Profile data loading error:', error);
    }
}

// Display functions
function displayRecentTransactions(transactions) {
    const container = document.getElementById('recent-transactions-list');
    
    if (transactions.length === 0) {
        container.innerHTML = '<p>No recent transactions</p>';
        return;
    }
    
    container.innerHTML = transactions.map(transaction => `
        <div class="transaction-item">
            <div class="transaction-info">
                <h4>${transaction.recipient_upi_id || 'Unknown'}</h4>
                <p>${transaction.description || 'No description'}</p>
                <p><small>${new Date(transaction.created_at).toLocaleDateString()}</small></p>
            </div>
            <div class="transaction-details">
                <div class="transaction-amount amount-sent">-₹${transaction.amount}</div>
                <div class="status ${transaction.status}">${transaction.status}</div>
            </div>
        </div>
    `).join('');
}

function displayTransactionHistory(transactions) {
    const container = document.getElementById('transaction-history');
    
    if (transactions.length === 0) {
        container.innerHTML = '<p>No transactions found</p>';
        return;
    }
    
    container.innerHTML = transactions.map(transaction => `
        <div class="transaction-item">
            <div class="transaction-info">
                <h4>${transaction.recipient_upi_id || 'Unknown'}</h4>
                <p>${transaction.description || 'No description'}</p>
                <p><small>${new Date(transaction.created_at).toLocaleDateString()}</small></p>
            </div>
            <div class="transaction-details">
                <div class="transaction-amount amount-sent">-₹${transaction.amount}</div>
                <div class="status ${transaction.status}">${transaction.status}</div>
            </div>
        </div>
    `).join('');
}

function displayProfileInfo(profile) {
    const container = document.getElementById('profile-info');
    container.innerHTML = `
        <div class="profile-field">
            <strong>Name:</strong> ${profile.full_name}
        </div>
        <div class="profile-field">
            <strong>Email:</strong> ${profile.email}
        </div>
        <div class="profile-field">
            <strong>Phone:</strong> ${profile.phone_number}
        </div>
        <div class="profile-field">
            <strong>Member Since:</strong> ${new Date(profile.created_at).toLocaleDateString()}
        </div>
    `;
}

// UI functions
function showPage(pageId) {
    // Hide all pages
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // Show selected page
    document.getElementById(`${pageId}-page`).classList.add('active');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Load page-specific data
    if (pageId === 'dashboard') {
        loadDashboardData();
    } else if (pageId === 'history') {
        loadTransactionHistory();
    } else if (pageId === 'profile') {
        loadProfileData();
    }
}

function showAuthenticatedApp() {
    document.getElementById('navbar').style.display = 'block';
    showPage('dashboard');
}

function showOtpModal() {
    document.getElementById('otp-modal').style.display = 'block';
    // Clear previous OTP inputs
    document.querySelectorAll('.otp-input').forEach(input => {
        input.value = '';
    });
    // Focus first input
    document.querySelector('.otp-input').focus();
}

function closeOtpModal() {
    document.getElementById('otp-modal').style.display = 'none';
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    document.getElementById('toast-container').appendChild(toast);
    
    // Remove toast after 4 seconds
    setTimeout(() => {
        toast.remove();
    }, 4000);
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('otp-modal');
    if (event.target === modal) {
        closeOtpModal();
    }
}
