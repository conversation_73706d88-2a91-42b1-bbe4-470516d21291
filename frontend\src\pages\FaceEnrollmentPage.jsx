import React, { useState, useRef, useCallback } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  Card,
  CardContent,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  CameraAlt,
  Refresh,
  CheckCircle,
  Security,
  FaceRetouchingNatural,
  Visibility,
  LightMode,
} from '@mui/icons-material';
import Webcam from 'react-webcam';
import { motion } from 'framer-motion';
import { useMutation, useQuery } from 'react-query';
import toast from 'react-hot-toast';
import { faceRecognitionAPI } from '../services/api';

const steps = ['Instructions', 'Capture Photo', 'Enrollment Complete'];

const FaceEnrollmentPage = () => {
  const webcamRef = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [imageSrc, setImageSrc] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);

  // Check current face recognition status
  const { data: faceStatus, refetch: refetchStatus } = useQuery(
    'faceRecognitionStatus',
    faceRecognitionAPI.getStatus
  );

  // Enroll face mutation
  const enrollFaceMutation = useMutation(faceRecognitionAPI.enroll, {
    onSuccess: () => {
      toast.success('Face enrolled successfully!');
      setActiveStep(2);
      refetchStatus();
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Face enrollment failed';
      toast.error(errorMessage);
    },
  });

  const videoConstraints = {
    width: 640,
    height: 480,
    facingMode: 'user',
  };

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();
    setImageSrc(imageSrc);
  }, [webcamRef]);

  const retakePhoto = () => {
    setImageSrc(null);
  };

  const handleEnrollFace = () => {
    if (!imageSrc) {
      toast.error('Please capture a photo first');
      return;
    }

    // Remove data URL prefix for API
    const base64Image = imageSrc.split(',')[1];
    
    enrollFaceMutation.mutate({
      image_data: base64Image,
      template_name: 'primary',
    });
  };

  const onUserMedia = () => {
    setCameraReady(true);
  };

  const onUserMediaError = (error) => {
    console.error('Camera error:', error);
    toast.error('Camera access denied. Please allow camera access and try again.');
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom align="center">
              Face Recognition Setup
            </Typography>
            
            <Typography variant="body1" paragraph>
              Face recognition adds an extra layer of security for high-value transactions (above ₹20,000).
              Follow these guidelines for the best results:
            </Typography>

            <List>
              <ListItem>
                <ListItemIcon>
                  <LightMode color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Good Lighting"
                  secondary="Ensure your face is well-lit and clearly visible"
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon>
                  <Visibility color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Look Directly at Camera"
                  secondary="Face the camera straight on with eyes open"
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon>
                  <FaceRetouchingNatural color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Remove Face Coverings"
                  secondary="Take off glasses, masks, or hats if possible"
                />
              </ListItem>
            </List>

            <Alert severity="info" sx={{ mt: 2 }}>
              Your face data is encrypted and stored securely. It's only used for transaction verification.
            </Alert>

            <Button
              fullWidth
              variant="contained"
              onClick={() => setActiveStep(1)}
              sx={{ mt: 3, py: 1.5 }}
              startIcon={<CameraAlt />}
            >
              Start Face Enrollment
            </Button>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom align="center">
              Capture Your Photo
            </Typography>
            
            <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
              Position your face in the center of the frame and capture a clear photo
            </Typography>

            <Card sx={{ mb: 3 }}>
              <CardContent sx={{ textAlign: 'center' }}>
                {!imageSrc ? (
                  <Box>
                    <Webcam
                      audio={false}
                      ref={webcamRef}
                      screenshotFormat="image/jpeg"
                      videoConstraints={videoConstraints}
                      onUserMedia={onUserMedia}
                      onUserMediaError={onUserMediaError}
                      style={{
                        width: '100%',
                        maxWidth: 400,
                        borderRadius: 8,
                      }}
                    />
                    
                    {cameraReady && (
                      <Button
                        variant="contained"
                        startIcon={<CameraAlt />}
                        onClick={capture}
                        sx={{ mt: 2 }}
                        size="large"
                      >
                        Capture Photo
                      </Button>
                    )}
                  </Box>
                ) : (
                  <Box>
                    <img
                      src={imageSrc}
                      alt="Captured"
                      style={{
                        width: '100%',
                        maxWidth: 400,
                        borderRadius: 8,
                      }}
                    />
                    
                    <Box sx={{ mt: 2, display: 'flex', gap: 2, justifyContent: 'center' }}>
                      <Button
                        variant="outlined"
                        startIcon={<Refresh />}
                        onClick={retakePhoto}
                        disabled={enrollFaceMutation.isLoading}
                      >
                        Retake
                      </Button>
                      
                      <Button
                        variant="contained"
                        onClick={handleEnrollFace}
                        disabled={enrollFaceMutation.isLoading}
                        size="large"
                        startIcon={<Security />}
                      >
                        {enrollFaceMutation.isLoading ? (
                          <>
                            <CircularProgress size={20} sx={{ mr: 1 }} />
                            Enrolling...
                          </>
                        ) : (
                          'Enroll Face'
                        )}
                      </Button>
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Box>
        );

      case 2:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" color="success.main" gutterBottom>
              Face Recognition Enabled!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Your face has been successfully enrolled. High-value transactions will now require face verification.
            </Typography>
            
            <Alert severity="success" sx={{ mb: 3 }}>
              Face recognition is now active for transactions above ₹20,000
            </Alert>
            
            <Button
              variant="contained"
              onClick={() => window.location.href = '/dashboard'}
              size="large"
            >
              Go to Dashboard
            </Button>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Container component="main" maxWidth="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper elevation={4} sx={{ p: 4 }}>
          <Typography component="h1" variant="h4" gutterBottom align="center">
            Face Recognition Setup
          </Typography>

          {/* Current Status */}
          {faceStatus?.data && (
            <Alert 
              severity={faceStatus.data.success ? "success" : "info"} 
              sx={{ mb: 3 }}
            >
              {faceStatus.data.message}
            </Alert>
          )}

          {/* Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Step Content */}
          {renderStepContent()}
        </Paper>
      </motion.div>
    </Container>
  );
};

export default FaceEnrollmentPage;
