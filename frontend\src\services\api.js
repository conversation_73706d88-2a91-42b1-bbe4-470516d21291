import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-storage');
    if (token) {
      try {
        const authData = JSON.parse(token);
        if (authData.state?.token) {
          config.headers.Authorization = `Bearer ${authData.state.token}`;
        }
      } catch (error) {
        console.error('Error parsing auth token:', error);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const authStorage = localStorage.getItem('auth-storage');
        if (authStorage) {
          const authData = JSON.parse(authStorage);
          const refreshToken = authData.state?.refreshToken;
          
          if (refreshToken) {
            const response = await axios.post(
              `${api.defaults.baseURL}/auth/refresh`,
              {},
              {
                headers: {
                  Authorization: `Bearer ${refreshToken}`,
                },
              }
            );

            const { access_token, refresh_token } = response.data;
            
            // Update stored tokens
            authData.state.token = access_token;
            authData.state.refreshToken = refresh_token;
            localStorage.setItem('auth-storage', JSON.stringify(authData));

            // Retry original request
            originalRequest.headers.Authorization = `Bearer ${access_token}`;
            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        localStorage.removeItem('auth-storage');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  getCurrentUser: (token) => 
    api.get('/auth/me', {
      headers: { Authorization: `Bearer ${token}` },
    }),
  refreshToken: (refreshToken) =>
    api.post('/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${refreshToken}` },
    }),
  verifyEmail: () => api.post('/auth/verify-email'),
  verifyPhone: () => api.post('/auth/verify-phone'),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData) => api.put('/users/profile', userData),
  deactivateAccount: () => api.delete('/users/account'),
};

// Transaction API
export const transactionAPI = {
  create: (transactionData) => api.post('/transactions/create', transactionData),
  process: (transactionId) => api.post(`/transactions/${transactionId}/process`),
  getHistory: (params = {}) => api.get('/transactions/history', { params }),
  getById: (transactionId) => api.get(`/transactions/${transactionId}`),
  cancel: (transactionId) => api.post(`/transactions/${transactionId}/cancel`),
  getStatistics: () => api.get('/transactions/statistics/summary'),
  getPendingCount: () => api.get('/transactions/pending/count'),
};

// OTP API
export const otpAPI = {
  send: (otpData) => api.post('/otp/send', otpData),
  verify: (verificationData) => api.post('/otp/verify', verificationData),
  getStatus: (params) => api.get('/otp/status', { params }),
  resend: (otpData) => api.post('/otp/resend', otpData),
};

// Face Recognition API
export const faceRecognitionAPI = {
  enroll: (enrollmentData) => api.post('/face-recognition/enroll', enrollmentData),
  verify: (verificationData) => api.post('/face-recognition/verify', verificationData),
  getStatus: () => api.get('/face-recognition/status'),
  deleteTemplate: (templateId) => api.delete(`/face-recognition/template/${templateId}`),
  listTemplates: () => api.get('/face-recognition/templates'),
};

// Health API
export const healthAPI = {
  check: () => api.get('/health'),
  detailed: () => api.get('/health/detailed'),
  info: () => api.get('/info'),
};

export default api;
