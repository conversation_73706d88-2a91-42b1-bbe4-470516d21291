"""
Transaction service for UPI payment processing
"""
import uuid
from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from app.models.user import User
from app.models.transaction import Transaction, TransactionStatus, TransactionType
from app.models.schemas import TransactionCreate
from app.core.config import settings
from app.services.notification_service import NotificationService


class TransactionService:
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService()
    
    def create_transaction(
        self, 
        sender: User, 
        transaction_data: TransactionCreate
    ) -> Transaction:
        """
        Create a new UPI transaction
        """
        # Validate sender
        if not sender.is_verified:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User must be verified to send money"
            )
        
        if not sender.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Account is deactivated"
            )
        
        # Validate transaction amount
        if transaction_data.amount <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Transaction amount must be greater than 0"
            )
        
        if transaction_data.amount > sender.per_transaction_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Transaction amount exceeds per-transaction limit of ₹{sender.per_transaction_limit}"
            )
        
        # Check daily transaction limit
        daily_total = self.get_daily_transaction_total(sender)
        if daily_total + transaction_data.amount > sender.daily_transaction_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Transaction would exceed daily limit of ₹{sender.daily_transaction_limit}"
            )
        
        # Find receiver by UPI ID
        receiver = self.db.query(User).filter(
            User.upi_id == transaction_data.receiver_upi_id
        ).first()
        
        # Generate unique transaction ID
        transaction_id = self.generate_transaction_id()
        
        # Determine if face verification is required
        requires_face_verification = (
            transaction_data.amount > settings.HIGH_VALUE_TRANSACTION_LIMIT and
            sender.face_recognition_enabled
        )
        
        # Set transaction expiry (30 minutes for pending transactions)
        expires_at = datetime.utcnow() + timedelta(minutes=30)
        
        # Create transaction
        transaction = Transaction(
            transaction_id=transaction_id,
            sender_id=sender.id,
            receiver_id=receiver.id if receiver else None,
            receiver_upi_id=transaction_data.receiver_upi_id,
            amount=transaction_data.amount,
            description=transaction_data.description,
            transaction_type=transaction_data.transaction_type,
            status=TransactionStatus.PENDING,
            requires_face_verification=requires_face_verification,
            expires_at=expires_at
        )
        
        self.db.add(transaction)
        self.db.commit()
        self.db.refresh(transaction)
        
        return transaction
    
    def process_transaction(self, transaction: Transaction) -> Transaction:
        """
        Process transaction after all verifications are complete
        """
        # Verify all required verifications are complete
        if not transaction.otp_verified:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OTP verification required"
            )
        
        if transaction.requires_face_verification and not transaction.face_verified:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Face verification required for high-value transactions"
            )
        
        # Check if transaction is still valid
        if transaction.expires_at and transaction.expires_at < datetime.utcnow():
            transaction.status = TransactionStatus.EXPIRED
            self.db.commit()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Transaction has expired"
            )
        
        # Update transaction status to processing
        transaction.status = TransactionStatus.PROCESSING
        self.db.commit()
        
        try:
            # Simulate UPI processing (in real implementation, this would call UPI APIs)
            success = self.simulate_upi_processing(transaction)
            
            if success:
                transaction.status = TransactionStatus.SUCCESS
                transaction.completed_at = datetime.utcnow()
                transaction.upi_ref_id = self.generate_upi_reference_id()
                
                # Send success notifications
                self.send_transaction_notifications(transaction, "success")
                
            else:
                transaction.status = TransactionStatus.FAILED
                transaction.failure_reason = "UPI processing failed"
                
                # Send failure notifications
                self.send_transaction_notifications(transaction, "failed")
            
            self.db.commit()
            
        except Exception as e:
            transaction.status = TransactionStatus.FAILED
            transaction.failure_reason = str(e)
            self.db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Transaction processing failed"
            )
        
        return transaction
    
    def get_user_transactions(
        self, 
        user: User, 
        limit: int = 50, 
        offset: int = 0,
        status_filter: Optional[TransactionStatus] = None
    ) -> List[Transaction]:
        """
        Get user's transaction history
        """
        query = self.db.query(Transaction).filter(
            (Transaction.sender_id == user.id) | (Transaction.receiver_id == user.id)
        )
        
        if status_filter:
            query = query.filter(Transaction.status == status_filter)
        
        return query.order_by(Transaction.created_at.desc()).offset(offset).limit(limit).all()
    
    def get_transaction_by_id(self, transaction_id: int, user: User) -> Optional[Transaction]:
        """
        Get transaction by ID (only if user is involved)
        """
        return self.db.query(Transaction).filter(
            Transaction.id == transaction_id,
            (Transaction.sender_id == user.id) | (Transaction.receiver_id == user.id)
        ).first()
    
    def cancel_transaction(self, transaction: Transaction, user: User) -> Transaction:
        """
        Cancel a pending transaction
        """
        # Only sender can cancel and only if transaction is pending
        if transaction.sender_id != user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only sender can cancel transaction"
            )
        
        if transaction.status not in [TransactionStatus.PENDING, TransactionStatus.OTP_REQUIRED, TransactionStatus.FACE_VERIFICATION_REQUIRED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot cancel transaction in current status"
            )
        
        transaction.status = TransactionStatus.CANCELLED
        self.db.commit()
        
        return transaction
    
    def get_daily_transaction_total(self, user: User) -> float:
        """
        Get total transaction amount for today
        """
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)
        
        total = self.db.query(Transaction).filter(
            Transaction.sender_id == user.id,
            Transaction.status == TransactionStatus.SUCCESS,
            Transaction.created_at >= today_start,
            Transaction.created_at < today_end
        ).with_entities(Transaction.amount).all()
        
        return sum(amount[0] for amount in total) if total else 0.0
    
    def generate_transaction_id(self) -> str:
        """
        Generate unique transaction ID
        """
        return f"UPI{datetime.utcnow().strftime('%Y%m%d')}{uuid.uuid4().hex[:8].upper()}"
    
    def generate_upi_reference_id(self) -> str:
        """
        Generate UPI reference ID (simulated)
        """
        return f"UPI{datetime.utcnow().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
    
    def simulate_upi_processing(self, transaction: Transaction) -> bool:
        """
        Simulate UPI processing (replace with actual UPI API calls)
        """
        # In a real implementation, this would:
        # 1. Call UPI APIs
        # 2. Handle bank responses
        # 3. Manage transaction states
        # 4. Handle failures and retries
        
        # For simulation, we'll assume 95% success rate
        import random
        return random.random() < 0.95
    
    def send_transaction_notifications(self, transaction: Transaction, status: str):
        """
        Send transaction notifications to sender and receiver
        """
        try:
            sender = self.db.query(User).filter(User.id == transaction.sender_id).first()
            receiver = self.db.query(User).filter(User.id == transaction.receiver_id).first() if transaction.receiver_id else None
            
            transaction_details = {
                "transaction_id": transaction.transaction_id,
                "amount": transaction.amount,
                "receiver": transaction.receiver_upi_id,
                "status": status
            }
            
            # Notify sender
            if sender:
                self.notification_service.send_transaction_notification(
                    email=sender.email,
                    phone_number=sender.phone_number,
                    transaction_details=transaction_details,
                    notification_type=status
                )
            
            # Notify receiver (if registered user)
            if receiver and status == "success":
                receiver_details = transaction_details.copy()
                receiver_details["sender"] = sender.upi_id if sender else "Unknown"
                
                self.notification_service.send_transaction_notification(
                    email=receiver.email,
                    phone_number=receiver.phone_number,
                    transaction_details=receiver_details,
                    notification_type="received"
                )
                
        except Exception as e:
            # Don't fail transaction if notification fails
            print(f"Failed to send notifications: {str(e)}")
    
    def get_transaction_statistics(self, user: User) -> dict:
        """
        Get user's transaction statistics
        """
        # Total transactions
        total_sent = self.db.query(Transaction).filter(
            Transaction.sender_id == user.id,
            Transaction.status == TransactionStatus.SUCCESS
        ).count()
        
        total_received = self.db.query(Transaction).filter(
            Transaction.receiver_id == user.id,
            Transaction.status == TransactionStatus.SUCCESS
        ).count()
        
        # Total amounts
        sent_amount = self.db.query(Transaction).filter(
            Transaction.sender_id == user.id,
            Transaction.status == TransactionStatus.SUCCESS
        ).with_entities(Transaction.amount).all()
        
        received_amount = self.db.query(Transaction).filter(
            Transaction.receiver_id == user.id,
            Transaction.status == TransactionStatus.SUCCESS
        ).with_entities(Transaction.amount).all()
        
        total_sent_amount = sum(amount[0] for amount in sent_amount) if sent_amount else 0.0
        total_received_amount = sum(amount[0] for amount in received_amount) if received_amount else 0.0
        
        # Today's transactions
        today_total = self.get_daily_transaction_total(user)
        
        return {
            "total_transactions_sent": total_sent,
            "total_transactions_received": total_received,
            "total_amount_sent": total_sent_amount,
            "total_amount_received": total_received_amount,
            "today_transaction_total": today_total,
            "daily_limit_remaining": user.daily_transaction_limit - today_total
        }
