"""
Database configuration and session management
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from .config import settings
import time
from typing import Dict, Any, Optional

# Database Engine (SQLite)
# SQLite-specific configuration for better performance and compatibility
if settings.DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        settings.DATABASE_URL,
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,  # Allow SQLite to be used with multiple threads
            "timeout": 20,  # Connection timeout in seconds
        },
        echo=settings.DEBUG,
    )
else:
    # Configuration for other databases (PostgreSQL, MySQL, etc.)
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=settings.DEBUG,
    )

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# In-memory cache replacement for Redis
class InMemoryCache:
    """Simple in-memory cache to replace Redis functionality"""

    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}

    def get(self, key: str) -> Optional[str]:
        """Get value from cache"""
        if key in self._cache:
            item = self._cache[key]
            if item['expires'] > time.time():
                return item['value']
            else:
                del self._cache[key]
        return None

    def setex(self, key: str, seconds: int, value: str) -> None:
        """Set value with expiration"""
        self._cache[key] = {
            'value': value,
            'expires': time.time() + seconds
        }

    def incr(self, key: str) -> int:
        """Increment value"""
        current = self.get(key)
        if current is None:
            new_value = 1
        else:
            new_value = int(current) + 1

        # Keep the same expiration if it exists
        if key in self._cache:
            expires = self._cache[key]['expires']
            self._cache[key] = {
                'value': str(new_value),
                'expires': expires
            }
        else:
            # Default 5 minute expiration for counters
            self.setex(key, 300, str(new_value))

        return new_value

    def ping(self) -> bool:
        """Health check - always returns True for in-memory cache"""
        return True

    def delete(self, key: str) -> None:
        """Delete key from cache"""
        if key in self._cache:
            del self._cache[key]

# Global in-memory cache instance
cache_client = InMemoryCache()


def get_db():
    """
    Dependency to get database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """
    Dependency to get cache client (in-memory replacement for Redis)
    """
    return cache_client
