"""
Main FastAPI application
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router
from app.core.middleware import (
    SecurityHeadersMiddleware,
    RateLimitMiddleware,
    RequestLoggingMiddleware,
    RequestSizeMiddleware,
    SQLInjectionProtectionMiddleware,
    CSRFProtectionMiddleware,
)

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    description="UPI Transaction App with multi-layer security",
    openapi_url="/api/v1/openapi.json",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc",
)

# Add security middleware (order matters!)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(RateLimitMiddleware, calls=100, period=60)
app.add_middleware(RequestSizeMiddleware, max_size=10 * 1024 * 1024)
app.add_middleware(SQLInjectionProtectionMiddleware)
app.add_middleware(CSRFProtectionMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware for security
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
)

# Include API router
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """
    Root endpoint
    """
    return {
        "message": "UPI Transaction App",
        "version": settings.VERSION,
        "docs": "/api/v1/docs"
    }


# Static file serving for frontend
from pathlib import Path

# Get the path to the frontend build directory
frontend_build_path = Path(__file__).parent.parent.parent / "frontend" / "dist"

# Health check endpoint (must be defined before catch-all routes)
@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {"status": "healthy", "version": settings.VERSION}


# Mount static files if the build directory exists
if frontend_build_path.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_build_path / "assets")), name="static")

    @app.get("/")
    async def serve_frontend():
        """Serve the React frontend"""
        index_file = frontend_build_path / "index.html"
        if index_file.exists():
            return FileResponse(str(index_file))
        return {"message": "Frontend not built. Please build the frontend first."}

    @app.get("/{full_path:path}")
    async def serve_frontend_routes(full_path: str):
        """Serve React routes (SPA fallback)"""
        # Don't intercept API routes
        if full_path.startswith("api/") or full_path.startswith("docs"):
            return {"error": "Not found"}

        index_file = frontend_build_path / "index.html"
        if index_file.exists():
            return FileResponse(str(index_file))
        return {"message": "Frontend not built. Please build the frontend first."}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
