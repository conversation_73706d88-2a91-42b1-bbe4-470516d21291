version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: upi_postgres
    environment:
      POSTGRES_DB: upi_transaction_db
      POSTGRES_USER: upi_user
      POSTGRES_PASSWORD: upi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - upi_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: upi_redis
    ports:
      - "6379:6379"
    networks:
      - upi_network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: upi_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************************/upi_transaction_db
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-here
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - upi_network

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: upi_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - upi_network

volumes:
  postgres_data:

networks:
  upi_network:
    driver: bridge
