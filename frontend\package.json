{"name": "upi-transaction-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-date-pickers": "^6.18.1", "dayjs": "^1.11.10", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-webcam": "^7.2.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0"}}