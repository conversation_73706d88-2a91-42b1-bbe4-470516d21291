import React, { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  Grid,
  Pagination,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  FilterList,
  Search,
  TrendingUp,
  TrendingDown,
  Schedule,
  CheckCircle,
  Cancel,
  Error,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { transactionAPI } from '../services/api';
import { useAuthStore } from '../store/authStore';

const TransactionHistoryPage = () => {
  const { user } = useAuthStore();
  const [page, setPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);

  const limit = 10;
  const offset = (page - 1) * limit;

  // Fetch transactions
  const { data: transactions, isLoading, error } = useQuery(
    ['transactions', page, statusFilter],
    () => transactionAPI.getHistory({
      limit,
      offset,
      status_filter: statusFilter || undefined,
    }),
    {
      keepPreviousData: true,
    }
  );

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircle color="success" />;
      case 'failed':
        return <Error color="error" />;
      case 'cancelled':
        return <Cancel color="disabled" />;
      case 'pending':
      case 'otp_required':
      case 'face_verification_required':
      case 'processing':
        return <Schedule color="warning" />;
      default:
        return <Schedule color="disabled" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'default';
      case 'pending':
      case 'otp_required':
      case 'face_verification_required':
        return 'warning';
      case 'processing':
        return 'info';
      default:
        return 'default';
    }
  };

  const getTransactionDirection = (transaction) => {
    return transaction.sender_id === user?.id ? 'sent' : 'received';
  };

  const filteredTransactions = transactions?.data?.filter(transaction => {
    if (!searchTerm) return true;
    return (
      transaction.receiver_upi_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.transaction_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }) || [];

  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    setPage(1);
    handleFilterClose();
  };

  if (isLoading) {
    return (
      <Container>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Alert severity="error" sx={{ mt: 2 }}>
          Failed to load transaction history. Please try again.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom>
          Transaction History
        </Typography>

        {/* Search and Filter */}
        <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            placeholder="Search transactions..."
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ flexGrow: 1 }}
          />
          
          <IconButton onClick={handleFilterClick}>
            <FilterList />
          </IconButton>
          
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterClose}
          >
            <MenuItem onClick={() => handleStatusFilter('')}>All Transactions</MenuItem>
            <MenuItem onClick={() => handleStatusFilter('success')}>Successful</MenuItem>
            <MenuItem onClick={() => handleStatusFilter('pending')}>Pending</MenuItem>
            <MenuItem onClick={() => handleStatusFilter('failed')}>Failed</MenuItem>
            <MenuItem onClick={() => handleStatusFilter('cancelled')}>Cancelled</MenuItem>
          </Menu>
        </Box>

        {/* Status Filter Chip */}
        {statusFilter && (
          <Box sx={{ mb: 2 }}>
            <Chip
              label={`Filter: ${statusFilter}`}
              onDelete={() => handleStatusFilter('')}
              color="primary"
              variant="outlined"
            />
          </Box>
        )}

        {/* Transactions List */}
        {filteredTransactions.length === 0 ? (
          <Alert severity="info">
            No transactions found. {searchTerm && 'Try adjusting your search terms.'}
          </Alert>
        ) : (
          <Grid container spacing={2}>
            {filteredTransactions.map((transaction, index) => {
              const direction = getTransactionDirection(transaction);
              const isOutgoing = direction === 'sent';
              
              return (
                <Grid item xs={12} key={transaction.id}>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card
                      sx={{
                        border: isOutgoing ? '1px solid #f44336' : '1px solid #4caf50',
                        borderLeft: isOutgoing ? '4px solid #f44336' : '4px solid #4caf50',
                      }}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Box sx={{ flexGrow: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              {isOutgoing ? (
                                <TrendingDown color="error" sx={{ mr: 1 }} />
                              ) : (
                                <TrendingUp color="success" sx={{ mr: 1 }} />
                              )}
                              <Typography variant="h6" color={isOutgoing ? 'error' : 'success'}>
                                {isOutgoing ? '-' : '+'}₹{transaction.amount.toLocaleString()}
                              </Typography>
                            </Box>
                            
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              {isOutgoing ? 'To: ' : 'From: '}{transaction.receiver_upi_id}
                            </Typography>
                            
                            {transaction.description && (
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                {transaction.description}
                              </Typography>
                            )}
                            
                            <Typography variant="caption" color="text.secondary">
                              {format(new Date(transaction.created_at), 'MMM dd, yyyy • hh:mm a')}
                            </Typography>
                            
                            <Typography variant="caption" color="text.secondary" display="block">
                              ID: {transaction.transaction_id}
                            </Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
                            <Chip
                              icon={getStatusIcon(transaction.status)}
                              label={transaction.status.replace('_', ' ').toUpperCase()}
                              color={getStatusColor(transaction.status)}
                              size="small"
                            />
                            
                            {transaction.requires_face_verification && (
                              <Chip
                                label="Face Verified"
                                size="small"
                                variant="outlined"
                                color={transaction.face_verified ? 'success' : 'warning'}
                              />
                            )}
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              );
            })}
          </Grid>
        )}

        {/* Pagination */}
        {filteredTransactions.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={Math.ceil((transactions?.data?.length || 0) / limit)}
              page={page}
              onChange={(event, value) => setPage(value)}
              color="primary"
            />
          </Box>
        )}
      </motion.div>
    </Container>
  );
};

export default TransactionHistoryPage;
