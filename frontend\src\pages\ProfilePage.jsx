import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Alert,
  Grid,
  Card,
  CardContent,
  Chip,
  Divider,
  Avatar,
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  AccountBalance,
  Security,
  Edit,
  Save,
  Cancel,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { userAPI, authAPI } from '../services/api';
import { useAuthStore } from '../store/authStore';

const ProfilePage = () => {
  const { user, updateUser } = useAuthStore();
  const [isEditing, setIsEditing] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      phone_number: user?.phone_number || '',
      bank_account_number: user?.bank_account_number || '',
      ifsc_code: user?.ifsc_code || '',
      bank_name: user?.bank_name || '',
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation(userAPI.updateProfile, {
    onSuccess: (response) => {
      updateUser(response.data);
      setIsEditing(false);
      toast.success('Profile updated successfully!');
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Failed to update profile';
      toast.error(errorMessage);
    },
  });

  // Verify email mutation
  const verifyEmailMutation = useMutation(authAPI.verifyEmail, {
    onSuccess: () => {
      updateUser({ email_verified: true });
      toast.success('Email verified successfully!');
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Email verification failed';
      toast.error(errorMessage);
    },
  });

  // Verify phone mutation
  const verifyPhoneMutation = useMutation(authAPI.verifyPhone, {
    onSuccess: () => {
      updateUser({ phone_verified: true });
      toast.success('Phone verified successfully!');
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Phone verification failed';
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data) => {
    updateProfileMutation.mutate(data);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    reset();
  };

  const getVerificationStatus = () => {
    if (user?.is_verified) {
      return { label: 'Fully Verified', color: 'success' };
    } else if (user?.email_verified && !user?.phone_verified) {
      return { label: 'Email Verified', color: 'warning' };
    } else if (!user?.email_verified && user?.phone_verified) {
      return { label: 'Phone Verified', color: 'warning' };
    } else {
      return { label: 'Unverified', color: 'error' };
    }
  };

  const verificationStatus = getVerificationStatus();

  return (
    <Container maxWidth="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom>
          Profile Settings
        </Typography>

        {/* Profile Header */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                  mr: 3,
                }}
              >
                {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
              </Avatar>
              
              <Box>
                <Typography variant="h5" gutterBottom>
                  {user?.first_name} {user?.last_name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {user?.email}
                </Typography>
                <Chip
                  label={verificationStatus.label}
                  color={verificationStatus.color}
                  size="small"
                  icon={<Security />}
                />
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Verification Status */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Account Verification
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Email sx={{ mr: 1 }} />
                    <Typography>Email Verification</Typography>
                  </Box>
                  {user?.email_verified ? (
                    <Chip label="Verified" color="success" size="small" />
                  ) : (
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => verifyEmailMutation.mutate()}
                      disabled={verifyEmailMutation.isLoading}
                    >
                      Verify
                    </Button>
                  )}
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Phone sx={{ mr: 1 }} />
                    <Typography>Phone Verification</Typography>
                  </Box>
                  {user?.phone_verified ? (
                    <Chip label="Verified" color="success" size="small" />
                  ) : (
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => verifyPhoneMutation.mutate()}
                      disabled={verifyPhoneMutation.isLoading}
                    >
                      Verify
                    </Button>
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Profile Information */}
        <Paper elevation={4} sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              Profile Information
            </Typography>
            
            {!isEditing ? (
              <Button
                variant="outlined"
                startIcon={<Edit />}
                onClick={() => setIsEditing(true)}
              >
                Edit Profile
              </Button>
            ) : (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<Cancel />}
                  onClick={handleCancelEdit}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={handleSubmit(onSubmit)}
                  disabled={updateProfileMutation.isLoading}
                >
                  Save
                </Button>
              </Box>
            )}
          </Box>

          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  disabled={!isEditing}
                  {...register('first_name', {
                    required: 'First name is required',
                  })}
                  error={!!errors.first_name}
                  helperText={errors.first_name?.message}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  disabled={!isEditing}
                  {...register('last_name', {
                    required: 'Last name is required',
                  })}
                  error={!!errors.last_name}
                  helperText={errors.last_name?.message}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Email Address"
                  value={user?.email}
                  disabled
                  helperText="Email cannot be changed"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  disabled={!isEditing}
                  {...register('phone_number', {
                    required: 'Phone number is required',
                  })}
                  error={!!errors.phone_number}
                  helperText={errors.phone_number?.message}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="UPI ID"
                  value={user?.upi_id}
                  disabled
                  helperText="UPI ID cannot be changed"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Banking Information
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Bank Account Number"
                  disabled={!isEditing}
                  {...register('bank_account_number')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="IFSC Code"
                  disabled={!isEditing}
                  {...register('ifsc_code')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Bank Name"
                  disabled={!isEditing}
                  {...register('bank_name')}
                />
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </motion.div>
    </Container>
  );
};

export default ProfilePage;
