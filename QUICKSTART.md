# 🚀 Quick Start Guide - UPI Transaction App

This guide will help you set up the UPI Transaction App with virtual environments on your local machine.

## 📋 Prerequisites

Before starting, ensure you have the following installed:

- **Python 3.9+** - [Download from python.org](https://python.org/)
- **Node.js 16+** - [Download from nodejs.org](https://nodejs.org/)
- **Git** - [Download from git-scm.com](https://git-scm.com/)

### Optional (for production deployment):
- **PostgreSQL** - [Download from postgresql.org](https://postgresql.org/)
- **Redis** - [Download from redis.io](https://redis.io/)
- **Docker** - [Download from docker.com](https://docker.com/)

## 🛠️ Setup Methods

Choose one of the following setup methods:

### Method 1: Automated Setup (Recommended)

#### For Windows:
```cmd
# Run the automated setup script
setup.bat
```

#### For Linux/Mac:
```bash
# Make the script executable and run it
chmod +x setup.sh
./setup.sh
```

#### For Cross-platform (Python):
```bash
# Run the Python setup script
python setup.py
```

### Method 2: Manual Setup

#### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd transaction
```

#### Step 2: Backend Setup
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

#### Step 3: Frontend Setup
```bash
# Navigate to frontend directory (from project root)
cd frontend

# Install dependencies
npm install

# Create environment file
cp .env.example .env
```

## ⚙️ Configuration

### Backend Configuration (backend/.env)

Edit `backend/.env` with your settings:

```env
# Application
APP_NAME=UPI Transaction App
VERSION=1.0.0
DEBUG=true

# Database (for development, you can use SQLite)
DATABASE_URL=postgresql://upi_user:upi_password@localhost:5432/upi_transaction_db
# Or use SQLite for quick testing:
# DATABASE_URL=sqlite:///./upi_app.db

# Redis (optional for development)
REDIS_URL=redis://localhost:6379

# Security (CHANGE IN PRODUCTION!)
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Transaction Limits
HIGH_VALUE_TRANSACTION_LIMIT=20000.0
DAILY_TRANSACTION_LIMIT=100000.0

# OTP Configuration
OTP_EXPIRE_MINUTES=5
OTP_LENGTH=6

# Twilio (for SMS OTP - optional)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# SendGrid (for Email OTP - optional)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Face Recognition
FACE_RECOGNITION_TOLERANCE=0.6
FACE_RECOGNITION_MODEL=hog

# CORS
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### Frontend Configuration (frontend/.env)

Edit `frontend/.env` if needed:

```env
VITE_API_URL=http://localhost:8000/api/v1
VITE_APP_NAME=UPI Transaction App
VITE_APP_VERSION=1.0.0
```

## 🚀 Running the Application

### Option 1: Using Activation Scripts

#### Windows:
```cmd
# Activate backend environment
activate.bat

# In the activated terminal:
cd backend
uvicorn app.main:app --reload

# In a new terminal:
cd frontend
npm run dev
```

#### Linux/Mac:
```bash
# Activate backend environment
./activate.sh

# In the activated terminal:
cd backend
uvicorn app.main:app --reload

# In a new terminal:
cd frontend
npm run dev
```

### Option 2: Manual Activation

#### Terminal 1 (Backend):
```bash
# Windows:
cd backend
venv\Scripts\activate
uvicorn app.main:app --reload

# Linux/Mac:
cd backend
source venv/bin/activate
uvicorn app.main:app --reload
```

#### Terminal 2 (Frontend):
```bash
cd frontend
npm run dev
```

### Option 3: Using Docker (Easiest)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🌐 Access the Application

Once both servers are running:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/v1/docs
- **Interactive API**: http://localhost:8000/api/v1/redoc

## 🗄️ Database Setup

### For Development (SQLite - No setup required)
The app will automatically create a SQLite database file.

### For Production (PostgreSQL)
1. Install PostgreSQL
2. Create database and user:
```sql
CREATE DATABASE upi_transaction_db;
CREATE USER upi_user WITH PASSWORD 'upi_password';
GRANT ALL PRIVILEGES ON DATABASE upi_transaction_db TO upi_user;
```
3. Update `DATABASE_URL` in `backend/.env`

### Redis Setup (Optional)
1. Install Redis
2. Start Redis server
3. Update `REDIS_URL` in `backend/.env`

## 🧪 Testing the Application

### Test User Registration
1. Go to http://localhost:3000
2. Click "Sign Up"
3. Fill in the registration form
4. Complete the registration

### Test Transaction Flow
1. Login with your account
2. Go to "Send Money"
3. Enter a UPI ID and amount
4. Follow the OTP verification process
5. For amounts > ₹20,000, complete face verification

## 🔧 Development Commands

### Backend Commands
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Run development server
uvicorn app.main:app --reload

# Run tests
pytest

# Format code
black app/
isort app/

# Check code quality
flake8 app/
```

### Frontend Commands
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Python/Node.js not found
- Ensure Python 3.9+ and Node.js 16+ are installed
- Check if they're added to your system PATH

#### 2. Virtual environment activation fails
```bash
# Windows: Use Command Prompt, not PowerShell
cmd
venv\Scripts\activate

# Linux/Mac: Ensure script is executable
chmod +x setup.sh
```

#### 3. Database connection errors
- For development, use SQLite (no setup required)
- For PostgreSQL, ensure the service is running
- Check database credentials in `.env`

#### 4. Camera not working
- Ensure you're using HTTPS or localhost
- Grant camera permissions in your browser
- Check if camera is being used by another application

#### 5. Face recognition installation issues
```bash
# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install build-essential cmake libopenblas-dev liblapack-dev libx11-dev libgtk-3-dev

# For Windows, install Visual Studio Build Tools
```

#### 6. Port already in use
```bash
# Check what's using the port
netstat -ano | findstr :8000  # Windows
lsof -i :8000                 # Linux/Mac

# Kill the process or use different ports
```

### Getting Help

1. Check the logs in the terminal
2. Review the API documentation at `/api/v1/docs`
3. Check the browser console for frontend errors
4. Ensure all environment variables are set correctly

## 🎯 Next Steps

After successful setup:

1. **Configure Services**: Set up Twilio for SMS and SendGrid for email
2. **Database**: Switch to PostgreSQL for production
3. **Security**: Change default secret keys
4. **Deployment**: Use Docker for production deployment
5. **Monitoring**: Set up logging and monitoring

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/)
- [Material-UI Documentation](https://mui.com/)
- [PostgreSQL Documentation](https://postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/)

---

🎉 **Congratulations!** You now have a fully functional UPI Transaction App with multi-layer security running on your local machine!
