import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Card,
  CardContent,
} from '@mui/material';
import { CameraAlt, Refresh, CheckCircle } from '@mui/icons-material';
import Webcam from 'react-webcam';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { faceRecognitionAPI } from '../../services/api';

const FaceVerification = ({ transaction, onVerified, onError }) => {
  const webcamRef = useRef(null);
  const [imageSrc, setImageSrc] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);
  const [verificationResult, setVerificationResult] = useState(null);

  // Face verification mutation
  const verifyFaceMutation = useMutation(faceRecognitionAPI.verify, {
    onSuccess: (response) => {
      const result = response.data;
      setVerificationResult(result);
      
      if (result.verification_successful) {
        toast.success('Face verification successful!');
        setTimeout(() => {
          onVerified();
        }, 2000);
      } else {
        toast.error(result.message || 'Face verification failed');
        onError(result.message || 'Face verification failed');
      }
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Face verification failed';
      onError(errorMessage);
      toast.error(errorMessage);
    },
  });

  const videoConstraints = {
    width: 640,
    height: 480,
    facingMode: 'user',
  };

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();
    setImageSrc(imageSrc);
  }, [webcamRef]);

  const retakePhoto = () => {
    setImageSrc(null);
    setVerificationResult(null);
  };

  const handleVerifyFace = () => {
    if (!imageSrc) {
      toast.error('Please capture a photo first');
      return;
    }

    // Remove data URL prefix for API
    const base64Image = imageSrc.split(',')[1];
    
    verifyFaceMutation.mutate({
      transaction_id: transaction.id,
      image_data: base64Image,
    });
  };

  const onUserMedia = () => {
    setCameraReady(true);
  };

  const onUserMediaError = (error) => {
    console.error('Camera error:', error);
    onError('Camera access denied. Please allow camera access and try again.');
  };

  if (verificationResult?.verification_successful) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
        <Typography variant="h6" color="success.main" gutterBottom>
          Face Verification Successful!
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Confidence: {(verificationResult.confidence_score * 100).toFixed(1)}%
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Processing transaction...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom align="center">
        Face Verification Required
      </Typography>
      
      <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
        This high-value transaction (₹{transaction?.amount?.toLocaleString()}) requires face verification for security.
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          • Position your face clearly in the camera frame
          • Ensure good lighting
          • Look directly at the camera
          • Remove any face coverings
        </Typography>
      </Alert>

      <Card sx={{ mb: 3 }}>
        <CardContent sx={{ textAlign: 'center' }}>
          {!imageSrc ? (
            <Box>
              <Webcam
                audio={false}
                ref={webcamRef}
                screenshotFormat="image/jpeg"
                videoConstraints={videoConstraints}
                onUserMedia={onUserMedia}
                onUserMediaError={onUserMediaError}
                style={{
                  width: '100%',
                  maxWidth: 400,
                  borderRadius: 8,
                }}
              />
              
              {cameraReady && (
                <Button
                  variant="contained"
                  startIcon={<CameraAlt />}
                  onClick={capture}
                  sx={{ mt: 2 }}
                  size="large"
                >
                  Capture Photo
                </Button>
              )}
            </Box>
          ) : (
            <Box>
              <img
                src={imageSrc}
                alt="Captured"
                style={{
                  width: '100%',
                  maxWidth: 400,
                  borderRadius: 8,
                }}
              />
              
              <Box sx={{ mt: 2, display: 'flex', gap: 2, justifyContent: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<Refresh />}
                  onClick={retakePhoto}
                  disabled={verifyFaceMutation.isLoading}
                >
                  Retake
                </Button>
                
                <Button
                  variant="contained"
                  onClick={handleVerifyFace}
                  disabled={verifyFaceMutation.isLoading}
                  size="large"
                >
                  {verifyFaceMutation.isLoading ? (
                    <>
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      Verifying...
                    </>
                  ) : (
                    'Verify Face'
                  )}
                </Button>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {verificationResult && !verificationResult.verification_successful && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="body2">
            {verificationResult.message}
          </Typography>
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Confidence: {(verificationResult.confidence_score * 100).toFixed(1)}%
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default FaceVerification;
