"""
User model for authentication and profile management
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    phone_number = Column(String(15), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    date_of_birth = Column(DateTime, nullable=True)
    
    # UPI Information
    upi_id = Column(String(100), unique=True, index=True, nullable=False)
    bank_account_number = Column(String(20), nullable=True)
    ifsc_code = Column(String(11), nullable=True)
    bank_name = Column(String(100), nullable=True)
    
    # Account Status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    email_verified = Column(Boolean, default=False)
    phone_verified = Column(Boolean, default=False)
    
    # Security
    failed_login_attempts = Column(Integer, default=0)
    account_locked_until = Column(DateTime, nullable=True)
    
    # Face Recognition
    face_encoding = Column(Text, nullable=True)  # Stored as JSON string
    face_recognition_enabled = Column(Boolean, default=False)
    
    # Transaction Limits
    daily_transaction_limit = Column(Float, default=100000.0)
    per_transaction_limit = Column(Float, default=50000.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime, nullable=True)
    
    # Relationships
    transactions_sent = relationship("Transaction", foreign_keys="Transaction.sender_id", back_populates="sender")
    transactions_received = relationship("Transaction", foreign_keys="Transaction.receiver_id", back_populates="receiver")
    otp_records = relationship("OTPRecord", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, upi_id={self.upi_id})>"
