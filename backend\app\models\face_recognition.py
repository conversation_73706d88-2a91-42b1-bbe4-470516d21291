"""
Face Recognition model for biometric verification
"""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, ForeignKey, Text, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class FaceRecognitionRecord(Base):
    __tablename__ = "face_recognition_records"

    id = Column(Integer, primary_key=True, index=True)
    
    # Associated User and Transaction
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    transaction_id = Column(Integer, ForeignKey("transactions.id"), nullable=True)
    
    # Face Recognition Data
    face_encoding = Column(Text, nullable=False)  # JSON string of face encoding
    confidence_score = Column(Float, nullable=False)  # Matching confidence (0-1)
    verification_successful = Column(Boolean, nullable=False)
    
    # Image Information
    image_hash = Column(String(64), nullable=True)  # SHA-256 hash of the image
    image_quality_score = Column(Float, nullable=True)  # Quality assessment (0-1)
    
    # Device and Environment
    device_info = Column(Text, nullable=True)  # JSON string with device details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Verification Context
    verification_type = Column(String(50), nullable=False)  # "transaction", "login", "enrollment"
    failure_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User")
    transaction = relationship("Transaction")
    
    def __repr__(self):
        return f"<FaceRecognitionRecord(id={self.id}, user_id={self.user_id}, successful={self.verification_successful})>"


class FaceTemplate(Base):
    """
    Store multiple face templates for a user to improve recognition accuracy
    """
    __tablename__ = "face_templates"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Template Data
    face_encoding = Column(Text, nullable=False)  # JSON string of face encoding
    template_name = Column(String(100), nullable=True)  # e.g., "primary", "backup_1"
    quality_score = Column(Float, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_primary = Column(Boolean, default=False)
    
    # Metadata
    enrollment_device = Column(Text, nullable=True)
    enrollment_conditions = Column(Text, nullable=True)  # lighting, angle, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_used = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<FaceTemplate(id={self.id}, user_id={self.user_id}, is_primary={self.is_primary})>"
