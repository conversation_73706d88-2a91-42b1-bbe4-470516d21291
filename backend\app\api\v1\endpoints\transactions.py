"""
Transaction endpoints for UPI payment processing
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.dependencies import get_current_verified_user
from app.models.schemas import (
    TransactionCreate,
    TransactionResponse,
    MessageResponse
)
from app.models.user import User
from app.models.transaction import Transaction, TransactionStatus
from app.services.transaction_service import TransactionService

router = APIRouter()


def get_transaction_service(db: Session = Depends(get_db)) -> TransactionService:
    """
    Get Transaction service instance
    """
    return TransactionService(db)


@router.post("/create", response_model=TransactionResponse, status_code=status.HTTP_201_CREATED)
async def create_transaction(
    transaction_data: TransactionCreate,
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Create a new UPI transaction
    """
    try:
        transaction = transaction_service.create_transaction(
            sender=current_user,
            transaction_data=transaction_data
        )
        
        return transaction
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create transaction"
        )


@router.post("/{transaction_id}/process", response_model=TransactionResponse)
async def process_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Process transaction after all verifications are complete
    """
    # Get transaction
    transaction = transaction_service.get_transaction_by_id(transaction_id, current_user)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Only sender can process transaction
    if transaction.sender_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only sender can process transaction"
        )
    
    try:
        processed_transaction = transaction_service.process_transaction(transaction)
        return processed_transaction
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process transaction"
        )


@router.get("/history", response_model=List[TransactionResponse])
async def get_transaction_history(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[str] = Query(None),
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get user's transaction history
    """
    # Parse status filter
    status_enum = None
    if status_filter:
        try:
            status_enum = TransactionStatus(status_filter)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid status filter"
            )
    
    transactions = transaction_service.get_user_transactions(
        user=current_user,
        limit=limit,
        offset=offset,
        status_filter=status_enum
    )
    
    return transactions


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get transaction details by ID
    """
    transaction = transaction_service.get_transaction_by_id(transaction_id, current_user)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    return transaction


@router.post("/{transaction_id}/cancel", response_model=TransactionResponse)
async def cancel_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Cancel a pending transaction
    """
    # Get transaction
    transaction = transaction_service.get_transaction_by_id(transaction_id, current_user)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    try:
        cancelled_transaction = transaction_service.cancel_transaction(transaction, current_user)
        return cancelled_transaction
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel transaction"
        )


@router.get("/statistics/summary")
async def get_transaction_statistics(
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get user's transaction statistics
    """
    try:
        stats = transaction_service.get_transaction_statistics(current_user)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get transaction statistics"
        )


@router.get("/pending/count")
async def get_pending_transactions_count(
    current_user: User = Depends(get_current_verified_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get count of pending transactions
    """
    pending_transactions = transaction_service.get_user_transactions(
        user=current_user,
        status_filter=TransactionStatus.PENDING
    )
    
    otp_required = transaction_service.get_user_transactions(
        user=current_user,
        status_filter=TransactionStatus.OTP_REQUIRED
    )
    
    face_verification_required = transaction_service.get_user_transactions(
        user=current_user,
        status_filter=TransactionStatus.FACE_VERIFICATION_REQUIRED
    )
    
    return {
        "pending_count": len(pending_transactions),
        "otp_required_count": len(otp_required),
        "face_verification_required_count": len(face_verification_required),
        "total_pending": len(pending_transactions) + len(otp_required) + len(face_verification_required)
    }
