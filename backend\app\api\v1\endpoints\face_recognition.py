"""
Face Recognition endpoints for biometric verification
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.models.schemas import (
    FaceRecognitionRequest,
    FaceRecognitionResponse,
    FaceEnrollmentRequest,
    MessageResponse
)
from app.models.user import User
from app.models.transaction import Transaction
from app.services.face_recognition_service import FaceRecognitionService

router = APIRouter()


def get_face_recognition_service(db: Session = Depends(get_db)) -> FaceRecognitionService:
    """
    Get Face Recognition service instance
    """
    return FaceRecognitionService(db)


@router.post("/enroll", response_model=MessageResponse)
async def enroll_face(
    enrollment_request: FaceEnrollmentRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    face_service: FaceRecognitionService = Depends(get_face_recognition_service)
):
    """
    Enroll user's face for biometric verification
    """
    try:
        # Get device info
        device_info = {
            "user_agent": request.headers.get("user-agent"),
            "ip_address": request.client.host
        }
        
        face_template = face_service.enroll_face(
            user=current_user,
            image_data=enrollment_request.image_data,
            template_name=enrollment_request.template_name,
            device_info=str(device_info)
        )
        
        return MessageResponse(
            message=f"Face enrolled successfully. Template ID: {face_template.id}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to enroll face"
        )


@router.post("/verify", response_model=FaceRecognitionResponse)
async def verify_face(
    verification_request: FaceRecognitionRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    face_service: FaceRecognitionService = Depends(get_face_recognition_service),
    db: Session = Depends(get_db)
):
    """
    Verify user's face for transaction authorization
    """
    # Get transaction
    transaction = db.query(Transaction).filter(
        Transaction.id == verification_request.transaction_id,
        Transaction.sender_id == current_user.id
    ).first()
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Check if transaction requires face verification
    if not transaction.requires_face_verification:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Face verification not required for this transaction"
        )
    
    # Check if OTP is verified first
    if not transaction.otp_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="OTP verification required before face verification"
        )
    
    try:
        # Get device info
        device_info = {
            "user_agent": request.headers.get("user-agent"),
            "ip_address": request.client.host
        }
        
        is_verified, confidence_score, message = face_service.verify_face(
            user=current_user,
            image_data=verification_request.image_data,
            transaction=transaction,
            device_info=str(device_info),
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        return FaceRecognitionResponse(
            verification_successful=is_verified,
            confidence_score=confidence_score,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify face"
        )


@router.get("/status", response_model=MessageResponse)
async def get_face_recognition_status(
    current_user: User = Depends(get_current_user),
    face_service: FaceRecognitionService = Depends(get_face_recognition_service)
):
    """
    Get user's face recognition enrollment status
    """
    templates = face_service.get_user_face_templates(current_user)
    
    if not templates:
        return MessageResponse(
            message="Face recognition not enrolled",
            success=False
        )
    
    active_templates = len(templates)
    primary_template = next((t for t in templates if t.is_primary), None)
    
    status_message = f"Face recognition enabled with {active_templates} template(s)"
    if primary_template:
        status_message += f". Primary template quality: {primary_template.quality_score:.2f}"
    
    return MessageResponse(message=status_message)


@router.delete("/template/{template_id}", response_model=MessageResponse)
async def delete_face_template(
    template_id: int,
    current_user: User = Depends(get_current_user),
    face_service: FaceRecognitionService = Depends(get_face_recognition_service)
):
    """
    Delete a face template
    """
    success = face_service.delete_face_template(current_user, template_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Face template not found"
        )
    
    return MessageResponse(message="Face template deleted successfully")


@router.get("/templates")
async def list_face_templates(
    current_user: User = Depends(get_current_user),
    face_service: FaceRecognitionService = Depends(get_face_recognition_service)
):
    """
    List user's face templates
    """
    templates = face_service.get_user_face_templates(current_user)
    
    template_list = []
    for template in templates:
        template_list.append({
            "id": template.id,
            "template_name": template.template_name,
            "quality_score": template.quality_score,
            "is_primary": template.is_primary,
            "created_at": template.created_at,
            "last_used": template.last_used
        })
    
    return {
        "templates": template_list,
        "total_count": len(template_list),
        "face_recognition_enabled": current_user.face_recognition_enabled
    }
