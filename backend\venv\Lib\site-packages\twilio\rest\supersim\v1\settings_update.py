r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Supersim
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class SettingsUpdateInstance(InstanceResource):
    class Status(object):
        SCHEDULED = "scheduled"
        IN_PROGRESS = "in-progress"
        SUCCESSFUL = "successful"
        FAILED = "failed"

    """
    :ivar sid: The unique identifier of this Settings Update.
    :ivar iccid: The [ICCID](https://en.wikipedia.org/wiki/SIM_card#ICCID) associated with the SIM.
    :ivar sim_sid: The SID of the Super SIM to which this Settings Update was applied.
    :ivar status: 
    :ivar packages: Array containing the different Settings Packages that will be applied to the SIM after the update completes. Each object within the array indicates the name and the version of the Settings Package that will be on the SIM if the update is successful.
    :ivar date_completed: The time, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format, when the update successfully completed and the new settings were applied to the SIM.
    :ivar date_created: The date that this Settings Update was created, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date that this Settings Update was updated, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.iccid: Optional[str] = payload.get("iccid")
        self.sim_sid: Optional[str] = payload.get("sim_sid")
        self.status: Optional["SettingsUpdateInstance.Status"] = payload.get("status")
        self.packages: Optional[List[object]] = payload.get("packages")
        self.date_completed: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_completed")
        )
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Supersim.V1.SettingsUpdateInstance>"


class SettingsUpdatePage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> SettingsUpdateInstance:
        """
        Build an instance of SettingsUpdateInstance

        :param payload: Payload response from the API
        """
        return SettingsUpdateInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Supersim.V1.SettingsUpdatePage>"


class SettingsUpdateList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the SettingsUpdateList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/SettingsUpdates"

    def stream(
        self,
        sim: Union[str, object] = values.unset,
        status: Union["SettingsUpdateInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[SettingsUpdateInstance]:
        """
        Streams SettingsUpdateInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str sim: Filter the Settings Updates by a Super SIM's SID or UniqueName.
        :param &quot;SettingsUpdateInstance.Status&quot; status: Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(sim=sim, status=status, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        sim: Union[str, object] = values.unset,
        status: Union["SettingsUpdateInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[SettingsUpdateInstance]:
        """
        Asynchronously streams SettingsUpdateInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str sim: Filter the Settings Updates by a Super SIM's SID or UniqueName.
        :param &quot;SettingsUpdateInstance.Status&quot; status: Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            sim=sim, status=status, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        sim: Union[str, object] = values.unset,
        status: Union["SettingsUpdateInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[SettingsUpdateInstance]:
        """
        Lists SettingsUpdateInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str sim: Filter the Settings Updates by a Super SIM's SID or UniqueName.
        :param &quot;SettingsUpdateInstance.Status&quot; status: Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                sim=sim,
                status=status,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        sim: Union[str, object] = values.unset,
        status: Union["SettingsUpdateInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[SettingsUpdateInstance]:
        """
        Asynchronously lists SettingsUpdateInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str sim: Filter the Settings Updates by a Super SIM's SID or UniqueName.
        :param &quot;SettingsUpdateInstance.Status&quot; status: Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                sim=sim,
                status=status,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        sim: Union[str, object] = values.unset,
        status: Union["SettingsUpdateInstance.Status", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> SettingsUpdatePage:
        """
        Retrieve a single page of SettingsUpdateInstance records from the API.
        Request is executed immediately

        :param sim: Filter the Settings Updates by a Super SIM's SID or UniqueName.
        :param status: Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of SettingsUpdateInstance
        """
        data = values.of(
            {
                "Sim": sim,
                "Status": status,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return SettingsUpdatePage(self._version, response)

    async def page_async(
        self,
        sim: Union[str, object] = values.unset,
        status: Union["SettingsUpdateInstance.Status", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> SettingsUpdatePage:
        """
        Asynchronously retrieve a single page of SettingsUpdateInstance records from the API.
        Request is executed immediately

        :param sim: Filter the Settings Updates by a Super SIM's SID or UniqueName.
        :param status: Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of SettingsUpdateInstance
        """
        data = values.of(
            {
                "Sim": sim,
                "Status": status,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return SettingsUpdatePage(self._version, response)

    def get_page(self, target_url: str) -> SettingsUpdatePage:
        """
        Retrieve a specific page of SettingsUpdateInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of SettingsUpdateInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return SettingsUpdatePage(self._version, response)

    async def get_page_async(self, target_url: str) -> SettingsUpdatePage:
        """
        Asynchronously retrieve a specific page of SettingsUpdateInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of SettingsUpdateInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return SettingsUpdatePage(self._version, response)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Supersim.V1.SettingsUpdateList>"
