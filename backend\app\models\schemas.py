"""
Pydantic schemas for API request/response models
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, validator
from .transaction import TransactionStatus, TransactionType
from .otp import OTPType, OTPDeliveryMethod, OTPStatus


# User Schemas
class UserBase(BaseModel):
    email: EmailStr
    phone_number: str
    first_name: str
    last_name: str
    upi_id: str


class UserCreate(UserBase):
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v


class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
    bank_account_number: Optional[str] = None
    ifsc_code: Optional[str] = None
    bank_name: Optional[str] = None


class UserResponse(UserBase):
    id: int
    is_active: bool
    is_verified: bool
    email_verified: bool
    phone_verified: bool
    face_recognition_enabled: bool
    created_at: datetime
    last_login: Optional[datetime]
    
    class Config:
        from_attributes = True


# Authentication Schemas
class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    email: Optional[str] = None


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


# Transaction Schemas
class TransactionBase(BaseModel):
    receiver_upi_id: str
    amount: float
    description: Optional[str] = None
    transaction_type: TransactionType = TransactionType.SEND_MONEY
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be greater than 0')
        if v > 200000:  # Max transaction limit
            raise ValueError('Amount exceeds maximum transaction limit')
        return v


class TransactionCreate(TransactionBase):
    pass


class TransactionResponse(TransactionBase):
    id: int
    transaction_id: str
    sender_id: int
    receiver_id: Optional[int]
    status: TransactionStatus
    otp_verified: bool
    face_verified: bool
    requires_face_verification: bool
    created_at: datetime
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# OTP Schemas
class OTPRequest(BaseModel):
    transaction_id: Optional[int] = None
    otp_type: OTPType
    delivery_method: OTPDeliveryMethod = OTPDeliveryMethod.SMS


class OTPVerification(BaseModel):
    otp_code: str
    transaction_id: Optional[int] = None
    otp_type: OTPType


class OTPResponse(BaseModel):
    id: int
    otp_type: OTPType
    delivery_method: OTPDeliveryMethod
    status: OTPStatus
    expires_at: datetime
    verification_attempts: int
    max_attempts: int
    
    class Config:
        from_attributes = True


# Face Recognition Schemas
class FaceRecognitionRequest(BaseModel):
    transaction_id: int
    image_data: str  # Base64 encoded image


class FaceRecognitionResponse(BaseModel):
    verification_successful: bool
    confidence_score: float
    message: str


class FaceEnrollmentRequest(BaseModel):
    image_data: str  # Base64 encoded image
    template_name: Optional[str] = "primary"


# Generic Response Schemas
class MessageResponse(BaseModel):
    message: str
    success: bool = True


class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None
