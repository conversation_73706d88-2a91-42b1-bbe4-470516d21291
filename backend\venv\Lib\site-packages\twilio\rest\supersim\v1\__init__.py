r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Supersim
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.supersim.v1.esim_profile import EsimProfileList
from twilio.rest.supersim.v1.fleet import FleetList
from twilio.rest.supersim.v1.ip_command import IpCommandList
from twilio.rest.supersim.v1.network import NetworkList
from twilio.rest.supersim.v1.network_access_profile import NetworkAccessProfileList
from twilio.rest.supersim.v1.settings_update import SettingsUpdateList
from twilio.rest.supersim.v1.sim import SimList
from twilio.rest.supersim.v1.sms_command import SmsCommandList
from twilio.rest.supersim.v1.usage_record import UsageRecordList


class V1(Version):
    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of Supersim

        :param domain: The Twilio.supersim domain
        """
        super().__init__(domain, "v1")
        self._esim_profiles: Optional[EsimProfileList] = None
        self._fleets: Optional[FleetList] = None
        self._ip_commands: Optional[IpCommandList] = None
        self._networks: Optional[NetworkList] = None
        self._network_access_profiles: Optional[NetworkAccessProfileList] = None
        self._settings_updates: Optional[SettingsUpdateList] = None
        self._sims: Optional[SimList] = None
        self._sms_commands: Optional[SmsCommandList] = None
        self._usage_records: Optional[UsageRecordList] = None

    @property
    def esim_profiles(self) -> EsimProfileList:
        if self._esim_profiles is None:
            self._esim_profiles = EsimProfileList(self)
        return self._esim_profiles

    @property
    def fleets(self) -> FleetList:
        if self._fleets is None:
            self._fleets = FleetList(self)
        return self._fleets

    @property
    def ip_commands(self) -> IpCommandList:
        if self._ip_commands is None:
            self._ip_commands = IpCommandList(self)
        return self._ip_commands

    @property
    def networks(self) -> NetworkList:
        if self._networks is None:
            self._networks = NetworkList(self)
        return self._networks

    @property
    def network_access_profiles(self) -> NetworkAccessProfileList:
        if self._network_access_profiles is None:
            self._network_access_profiles = NetworkAccessProfileList(self)
        return self._network_access_profiles

    @property
    def settings_updates(self) -> SettingsUpdateList:
        if self._settings_updates is None:
            self._settings_updates = SettingsUpdateList(self)
        return self._settings_updates

    @property
    def sims(self) -> SimList:
        if self._sims is None:
            self._sims = SimList(self)
        return self._sims

    @property
    def sms_commands(self) -> SmsCommandList:
        if self._sms_commands is None:
            self._sms_commands = SmsCommandList(self)
        return self._sms_commands

    @property
    def usage_records(self) -> UsageRecordList:
        if self._usage_records is None:
            self._usage_records = UsageRecordList(self)
        return self._usage_records

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Supersim.V1>"
