import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import { Sms, Email } from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { otpAPI } from '../../services/api';

const OTPVerification = ({ transaction, onVerified, onError }) => {
  const [otpSent, setOtpSent] = useState(false);
  const [deliveryMethod, setDeliveryMethod] = useState('sms');
  const [countdown, setCountdown] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();

  // Send OTP mutation
  const sendOTPMutation = useMutation(otpAPI.send, {
    onSuccess: () => {
      setOtpSent(true);
      setCountdown(60); // 60 second countdown
      toast.success(`OTP sent via ${deliveryMethod.toUpperCase()}`);
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Failed to send OTP';
      onError(errorMessage);
      toast.error(errorMessage);
    },
  });

  // Verify OTP mutation
  const verifyOTPMutation = useMutation(otpAPI.verify, {
    onSuccess: () => {
      toast.success('OTP verified successfully!');
      onVerified();
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Invalid OTP';
      onError(errorMessage);
      toast.error(errorMessage);
      reset();
    },
  });

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Auto-send OTP when component mounts
  useEffect(() => {
    if (transaction && !otpSent) {
      handleSendOTP();
    }
  }, [transaction]);

  const handleSendOTP = () => {
    sendOTPMutation.mutate({
      transaction_id: transaction.id,
      otp_type: 'transaction_verification',
      delivery_method: deliveryMethod,
    });
  };

  const onSubmit = (data) => {
    verifyOTPMutation.mutate({
      otp_code: data.otp_code,
      transaction_id: transaction.id,
      otp_type: 'transaction_verification',
    });
  };

  const handleResendOTP = () => {
    setOtpSent(false);
    reset();
    handleSendOTP();
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom align="center">
        OTP Verification
      </Typography>
      
      <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
        Please enter the OTP sent to verify your transaction of ₹{transaction?.amount?.toLocaleString()}
      </Typography>

      {!otpSent ? (
        <Box>
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel component="legend">Choose delivery method:</FormLabel>
            <RadioGroup
              value={deliveryMethod}
              onChange={(e) => setDeliveryMethod(e.target.value)}
              row
            >
              <FormControlLabel
                value="sms"
                control={<Radio />}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Sms sx={{ mr: 1 }} />
                    SMS
                  </Box>
                }
              />
              <FormControlLabel
                value="email"
                control={<Radio />}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Email sx={{ mr: 1 }} />
                    Email
                  </Box>
                }
              />
            </RadioGroup>
          </FormControl>

          <Button
            fullWidth
            variant="contained"
            onClick={handleSendOTP}
            disabled={sendOTPMutation.isLoading}
            sx={{ py: 1.5 }}
          >
            {sendOTPMutation.isLoading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Sending OTP...
              </>
            ) : (
              'Send OTP'
            )}
          </Button>
        </Box>
      ) : (
        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="otp_code"
            label="Enter OTP"
            autoComplete="one-time-code"
            autoFocus
            inputProps={{
              maxLength: 6,
              style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' },
            }}
            {...register('otp_code', {
              required: 'OTP is required',
              pattern: {
                value: /^\d{6}$/,
                message: 'OTP must be 6 digits',
              },
            })}
            error={!!errors.otp_code}
            helperText={errors.otp_code?.message}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 2, py: 1.5 }}
            disabled={verifyOTPMutation.isLoading}
          >
            {verifyOTPMutation.isLoading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Verifying...
              </>
            ) : (
              'Verify OTP'
            )}
          </Button>

          <Box sx={{ mt: 2, textAlign: 'center' }}>
            {countdown > 0 ? (
              <Typography variant="body2" color="text.secondary">
                Resend OTP in {countdown} seconds
              </Typography>
            ) : (
              <Button
                variant="text"
                onClick={handleResendOTP}
                disabled={sendOTPMutation.isLoading}
              >
                Resend OTP
              </Button>
            )}
          </Box>

          <Alert severity="info" sx={{ mt: 2 }}>
            OTP sent via {deliveryMethod.toUpperCase()}. Please check your {deliveryMethod === 'sms' ? 'messages' : 'email'}.
          </Alert>
        </Box>
      )}
    </Box>
  );
};

export default OTPVerification;
