"""
Custom validators for input validation and security
"""
import re
from typing import Optional
from fastapi import HTTPException, status


class ValidationError(Exception):
    """Custom validation error"""
    pass


def validate_upi_id(upi_id: str) -> bool:
    """
    Validate UPI ID format
    """
    pattern = r'^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$'
    return bool(re.match(pattern, upi_id))


def validate_phone_number(phone: str) -> bool:
    """
    Validate phone number format
    """
    # Remove any spaces, dashes, or parentheses
    cleaned_phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # Check if it's a valid international format
    pattern = r'^[+]?[1-9][\d]{9,14}$'
    return bool(re.match(pattern, cleaned_phone))


def validate_ifsc_code(ifsc: str) -> bool:
    """
    Validate IFSC code format
    """
    pattern = r'^[A-Z]{4}0[A-Z0-9]{6}$'
    return bool(re.match(pattern, ifsc.upper()))


def validate_transaction_amount(amount: float, user_limits: dict) -> None:
    """
    Validate transaction amount against user limits
    """
    if amount <= 0:
        raise ValidationError("Transaction amount must be greater than 0")
    
    if amount > user_limits.get('per_transaction_limit', 50000):
        raise ValidationError(f"Amount exceeds per-transaction limit of ₹{user_limits['per_transaction_limit']:,}")
    
    daily_used = user_limits.get('daily_used', 0)
    daily_limit = user_limits.get('daily_limit', 100000)
    
    if daily_used + amount > daily_limit:
        remaining = daily_limit - daily_used
        raise ValidationError(f"Amount exceeds daily limit. Remaining: ₹{remaining:,}")


def validate_password_strength(password: str) -> None:
    """
    Validate password strength
    """
    if len(password) < 8:
        raise ValidationError("Password must be at least 8 characters long")
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError("Password must contain at least one uppercase letter")
    
    if not re.search(r'[a-z]', password):
        raise ValidationError("Password must contain at least one lowercase letter")
    
    if not re.search(r'\d', password):
        raise ValidationError("Password must contain at least one digit")
    
    # Check for common weak passwords
    weak_passwords = [
        'password', '12345678', 'qwerty123', 'admin123',
        'password123', '123456789', 'welcome123'
    ]
    
    if password.lower() in weak_passwords:
        raise ValidationError("Password is too common. Please choose a stronger password")


def sanitize_input(text: str, max_length: int = 255) -> str:
    """
    Sanitize user input to prevent injection attacks
    """
    if not text:
        return ""
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\';\\]', '', text)
    
    # Limit length
    sanitized = sanitized[:max_length]
    
    # Strip whitespace
    sanitized = sanitized.strip()
    
    return sanitized


def validate_otp_code(otp: str) -> bool:
    """
    Validate OTP code format
    """
    pattern = r'^\d{6}$'
    return bool(re.match(pattern, otp))


def validate_transaction_description(description: Optional[str]) -> Optional[str]:
    """
    Validate and sanitize transaction description
    """
    if not description:
        return None
    
    # Sanitize input
    sanitized = sanitize_input(description, max_length=500)
    
    # Check for suspicious patterns
    suspicious_patterns = [
        r'<script',
        r'javascript:',
        r'on\w+\s*=',
        r'data:',
        r'vbscript:',
    ]
    
    for pattern in suspicious_patterns:
        if re.search(pattern, sanitized, re.IGNORECASE):
            raise ValidationError("Description contains invalid content")
    
    return sanitized


def validate_email_format(email: str) -> bool:
    """
    Validate email format
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_bank_account_number(account_number: str) -> bool:
    """
    Validate bank account number format
    """
    # Remove spaces and check if it's numeric
    cleaned = re.sub(r'\s', '', account_number)
    
    # Bank account numbers are typically 9-18 digits
    pattern = r'^\d{9,18}$'
    return bool(re.match(pattern, cleaned))


def rate_limit_check(user_id: int, action: str, redis_client, limit: int = 5, window: int = 300) -> None:
    """
    Check rate limiting for user actions
    """
    key = f"rate_limit:{user_id}:{action}"
    
    try:
        current_count = redis_client.get(key)
        
        if current_count is None:
            # First request
            redis_client.setex(key, window, 1)
        else:
            current_count = int(current_count)
            if current_count >= limit:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"Rate limit exceeded for {action}. Try again later."
                )
            else:
                redis_client.incr(key)
                
    except Exception as e:
        # If Redis is down, allow the request but log the error
        print(f"Rate limiting error: {e}")


def validate_face_image_data(image_data: str) -> None:
    """
    Validate face image data
    """
    if not image_data:
        raise ValidationError("Image data is required")
    
    # Check if it's base64 encoded
    try:
        import base64
        base64.b64decode(image_data)
    except Exception:
        raise ValidationError("Invalid image data format")
    
    # Check image size (approximate)
    # Base64 encoding increases size by ~33%
    estimated_size = len(image_data) * 0.75
    max_size = 5 * 1024 * 1024  # 5MB
    
    if estimated_size > max_size:
        raise ValidationError("Image size too large. Maximum 5MB allowed.")


def validate_device_info(device_info: dict) -> dict:
    """
    Validate and sanitize device information
    """
    if not isinstance(device_info, dict):
        return {}
    
    allowed_keys = ['user_agent', 'ip_address', 'platform', 'browser']
    sanitized = {}
    
    for key in allowed_keys:
        if key in device_info:
            value = str(device_info[key])
            sanitized[key] = sanitize_input(value, max_length=500)
    
    return sanitized
