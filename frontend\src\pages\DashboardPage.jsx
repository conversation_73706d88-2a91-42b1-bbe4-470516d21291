import React, { useEffect, useState } from 'react';
import {
  Con<PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Avatar,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Payment,
  History,
  AccountBalance,
  TrendingUp,
  Security,
  FaceRetouchingNatural,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { useAuthStore } from '../store/authStore';
import { transactionAPI } from '../services/api';

const DashboardPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();

  // Fetch transaction statistics
  const { data: stats, isLoading: statsLoading } = useQuery(
    'transactionStats',
    transactionAPI.getStatistics,
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  // Fetch pending transactions count
  const { data: pendingCount } = useQuery(
    'pendingTransactions',
    transactionAPI.getPendingCount,
    {
      refetchInterval: 10000, // Refresh every 10 seconds
    }
  );

  const quickActions = [
    {
      title: 'Send Money',
      description: 'Transfer money instantly',
      icon: <Payment />,
      color: '#1976d2',
      action: () => navigate('/transaction'),
    },
    {
      title: 'Transaction History',
      description: 'View all transactions',
      icon: <History />,
      color: '#388e3c',
      action: () => navigate('/history'),
    },
    {
      title: 'Face Recognition',
      description: 'Setup biometric security',
      icon: <FaceRetouchingNatural />,
      color: '#f57c00',
      action: () => navigate('/face-enrollment'),
    },
  ];

  const getVerificationStatus = () => {
    if (user?.is_verified) {
      return { label: 'Verified', color: 'success' };
    } else if (user?.email_verified && !user?.phone_verified) {
      return { label: 'Phone Pending', color: 'warning' };
    } else if (!user?.email_verified && user?.phone_verified) {
      return { label: 'Email Pending', color: 'warning' };
    } else {
      return { label: 'Unverified', color: 'error' };
    }
  };

  const verificationStatus = getVerificationStatus();

  return (
    <Container maxWidth="lg">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Welcome Section */}
        <Box sx={{ mb: 4 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar
                sx={{
                  width: 64,
                  height: 64,
                  bgcolor: 'primary.main',
                  fontSize: '1.5rem',
                }}
              >
                {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" gutterBottom>
                Welcome back, {user?.first_name}!
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="body1" color="text.secondary">
                  UPI ID: {user?.upi_id}
                </Typography>
                <Chip
                  label={verificationStatus.label}
                  color={verificationStatus.color}
                  size="small"
                  icon={<Security />}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* Pending Transactions Alert */}
        {pendingCount?.total_pending > 0 && (
          <Card sx={{ mb: 3, bgcolor: 'warning.light', color: 'warning.contrastText' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Pending Transactions
              </Typography>
              <Typography variant="body2">
                You have {pendingCount.total_pending} pending transaction(s) that require attention.
              </Typography>
              <Button
                variant="contained"
                color="warning"
                sx={{ mt: 2 }}
                onClick={() => navigate('/history')}
              >
                View Pending
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AccountBalance color="primary" />
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    Total Sent
                  </Typography>
                </Box>
                <Typography variant="h4" color="primary">
                  ₹{stats?.total_amount_sent?.toLocaleString() || '0'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {stats?.total_transactions_sent || 0} transactions
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUp color="success" />
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    Total Received
                  </Typography>
                </Box>
                <Typography variant="h4" color="success.main">
                  ₹{stats?.total_amount_received?.toLocaleString() || '0'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {stats?.total_transactions_received || 0} transactions
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Today's Usage
                </Typography>
                <Typography variant="h4" color="info.main">
                  ₹{stats?.today_transaction_total?.toLocaleString() || '0'}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Daily Limit: ₹{user?.daily_transaction_limit?.toLocaleString()}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={
                      user?.daily_transaction_limit
                        ? (stats?.today_transaction_total / user.daily_transaction_limit) * 100
                        : 0
                    }
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Remaining Limit
                </Typography>
                <Typography variant="h4" color="warning.main">
                  ₹{stats?.daily_limit_remaining?.toLocaleString() || '0'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Available today
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Quick Actions */}
        <Typography variant="h5" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={3}>
          {quickActions.map((action, index) => (
            <Grid item xs={12} sm={6} md={4} key={action.title}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    },
                  }}
                  onClick={action.action}
                >
                  <CardContent sx={{ textAlign: 'center', py: 4 }}>
                    <Avatar
                      sx={{
                        width: 64,
                        height: 64,
                        bgcolor: action.color,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {action.icon}
                    </Avatar>
                    <Typography variant="h6" gutterBottom>
                      {action.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {action.description}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </motion.div>
    </Container>
  );
};

export default DashboardPage;
