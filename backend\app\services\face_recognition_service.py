"""
Face Recognition service for biometric verification
"""
import base64
import json
import hashlib
import logging
from datetime import datetime
from typing import Optional, List, Tuple
import cv2
import numpy as np
import face_recognition
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from app.models.user import User
from app.models.transaction import Transaction
from app.models.face_recognition import FaceRecognitionRecord, FaceTemplate
from app.core.config import settings

logger = logging.getLogger(__name__)


class FaceRecognitionService:
    def __init__(self, db: Session):
        self.db = db
        self.tolerance = settings.FACE_RECOGNITION_TOLERANCE
        self.model = settings.FACE_RECOGNITION_MODEL
    
    def decode_base64_image(self, base64_string: str) -> np.ndarray:
        """
        Decode base64 image string to numpy array
        """
        try:
            # Remove data URL prefix if present
            if base64_string.startswith('data:image'):
                base64_string = base64_string.split(',')[1]
            
            # Decode base64
            image_data = base64.b64decode(base64_string)
            
            # Convert to numpy array
            nparr = np.frombuffer(image_data, np.uint8)
            
            # Decode image
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValueError("Failed to decode image")
            
            # Convert BGR to RGB (face_recognition expects RGB)
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            return image_rgb
            
        except Exception as e:
            logger.error(f"Failed to decode base64 image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid image format"
            )
    
    def extract_face_encoding(self, image: np.ndarray) -> Tuple[Optional[List[float]], float]:
        """
        Extract face encoding from image
        Returns: (face_encoding, quality_score)
        """
        try:
            # Find face locations
            face_locations = face_recognition.face_locations(image, model=self.model)
            
            if not face_locations:
                return None, 0.0
            
            # If multiple faces, use the largest one
            if len(face_locations) > 1:
                # Calculate face area and select largest
                largest_face = max(face_locations, key=lambda loc: (loc[2] - loc[0]) * (loc[1] - loc[3]))
                face_locations = [largest_face]
            
            # Extract face encodings
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            if not face_encodings:
                return None, 0.0
            
            face_encoding = face_encodings[0]
            
            # Calculate quality score based on face size and clarity
            face_location = face_locations[0]
            face_height = face_location[2] - face_location[0]
            face_width = face_location[1] - face_location[3]
            face_area = face_height * face_width
            
            # Normalize quality score (0-1)
            # Larger faces generally have better quality
            quality_score = min(1.0, face_area / (image.shape[0] * image.shape[1] * 0.1))
            
            return face_encoding.tolist(), quality_score
            
        except Exception as e:
            logger.error(f"Failed to extract face encoding: {str(e)}")
            return None, 0.0
    
    def enroll_face(
        self, 
        user: User, 
        image_data: str, 
        template_name: str = "primary",
        device_info: Optional[str] = None
    ) -> FaceTemplate:
        """
        Enroll user's face for recognition
        """
        # Decode image
        image = self.decode_base64_image(image_data)
        
        # Extract face encoding
        face_encoding, quality_score = self.extract_face_encoding(image)
        
        if face_encoding is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No face detected in the image"
            )
        
        if quality_score < 0.3:  # Minimum quality threshold
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Face image quality is too low. Please try again with better lighting."
            )
        
        # Check if user already has a primary template
        existing_primary = self.db.query(FaceTemplate).filter(
            FaceTemplate.user_id == user.id,
            FaceTemplate.is_primary == True,
            FaceTemplate.is_active == True
        ).first()
        
        is_primary = template_name == "primary" and not existing_primary
        
        # Create face template
        face_template = FaceTemplate(
            user_id=user.id,
            face_encoding=json.dumps(face_encoding),
            template_name=template_name,
            quality_score=quality_score,
            is_primary=is_primary,
            enrollment_device=device_info
        )
        
        self.db.add(face_template)
        
        # Update user's face recognition status
        if is_primary:
            user.face_encoding = json.dumps(face_encoding)
            user.face_recognition_enabled = True
        
        self.db.commit()
        self.db.refresh(face_template)
        
        return face_template
    
    def verify_face(
        self, 
        user: User, 
        image_data: str, 
        transaction: Optional[Transaction] = None,
        device_info: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, float, str]:
        """
        Verify user's face against enrolled templates
        Returns: (is_verified, confidence_score, message)
        """
        if not user.face_recognition_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Face recognition not enabled for this user"
            )
        
        # Decode image
        image = self.decode_base64_image(image_data)
        
        # Extract face encoding from verification image
        verification_encoding, image_quality = self.extract_face_encoding(image)
        
        if verification_encoding is None:
            # Record failed verification
            self._record_verification_attempt(
                user=user,
                transaction=transaction,
                face_encoding=None,
                confidence_score=0.0,
                verification_successful=False,
                failure_reason="No face detected",
                image_quality_score=0.0,
                device_info=device_info,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return False, 0.0, "No face detected in the image"
        
        if image_quality < 0.2:
            # Record failed verification
            self._record_verification_attempt(
                user=user,
                transaction=transaction,
                face_encoding=json.dumps(verification_encoding),
                confidence_score=0.0,
                verification_successful=False,
                failure_reason="Poor image quality",
                image_quality_score=image_quality,
                device_info=device_info,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return False, 0.0, "Image quality is too low. Please try again with better lighting."
        
        # Get user's enrolled face templates
        face_templates = self.db.query(FaceTemplate).filter(
            FaceTemplate.user_id == user.id,
            FaceTemplate.is_active == True
        ).all()
        
        if not face_templates:
            return False, 0.0, "No enrolled face templates found"
        
        # Compare against all templates
        best_match_distance = float('inf')
        best_confidence = 0.0
        
        for template in face_templates:
            try:
                enrolled_encoding = json.loads(template.face_encoding)
                
                # Calculate face distance
                distance = face_recognition.face_distance([enrolled_encoding], verification_encoding)[0]
                
                # Convert distance to confidence score (0-1)
                confidence = max(0.0, 1.0 - distance)
                
                if distance < best_match_distance:
                    best_match_distance = distance
                    best_confidence = confidence
                
                # Update template last used
                template.last_used = datetime.utcnow()
                
            except Exception as e:
                logger.error(f"Error comparing with template {template.id}: {str(e)}")
                continue
        
        # Determine if verification is successful
        is_verified = best_match_distance <= self.tolerance
        
        # Record verification attempt
        verification_record = self._record_verification_attempt(
            user=user,
            transaction=transaction,
            face_encoding=json.dumps(verification_encoding),
            confidence_score=best_confidence,
            verification_successful=is_verified,
            failure_reason=None if is_verified else f"Face match confidence too low: {best_confidence:.2f}",
            image_quality_score=image_quality,
            device_info=device_info,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.commit()
        
        if is_verified:
            # Update transaction face verification status
            if transaction:
                transaction.face_verified = True
                self.db.commit()
            
            return True, best_confidence, "Face verification successful"
        else:
            return False, best_confidence, f"Face verification failed. Confidence: {best_confidence:.2f}"
    
    def _record_verification_attempt(
        self,
        user: User,
        transaction: Optional[Transaction],
        face_encoding: Optional[str],
        confidence_score: float,
        verification_successful: bool,
        failure_reason: Optional[str],
        image_quality_score: float,
        device_info: Optional[str],
        ip_address: Optional[str],
        user_agent: Optional[str]
    ) -> FaceRecognitionRecord:
        """
        Record face verification attempt
        """
        # Generate image hash for security
        image_hash = None
        if face_encoding:
            image_hash = hashlib.sha256(face_encoding.encode()).hexdigest()
        
        verification_record = FaceRecognitionRecord(
            user_id=user.id,
            transaction_id=transaction.id if transaction else None,
            face_encoding=face_encoding or "",
            confidence_score=confidence_score,
            verification_successful=verification_successful,
            image_hash=image_hash,
            image_quality_score=image_quality_score,
            device_info=device_info,
            ip_address=ip_address,
            user_agent=user_agent,
            verification_type="transaction" if transaction else "general",
            failure_reason=failure_reason,
            processed_at=datetime.utcnow()
        )
        
        self.db.add(verification_record)
        return verification_record
    
    def get_user_face_templates(self, user: User) -> List[FaceTemplate]:
        """
        Get all active face templates for user
        """
        return self.db.query(FaceTemplate).filter(
            FaceTemplate.user_id == user.id,
            FaceTemplate.is_active == True
        ).all()
    
    def delete_face_template(self, user: User, template_id: int) -> bool:
        """
        Delete (deactivate) a face template
        """
        template = self.db.query(FaceTemplate).filter(
            FaceTemplate.id == template_id,
            FaceTemplate.user_id == user.id
        ).first()
        
        if not template:
            return False
        
        template.is_active = False
        
        # If this was the primary template, disable face recognition
        if template.is_primary:
            user.face_recognition_enabled = False
            user.face_encoding = None
        
        self.db.commit()
        return True
