"""
OTP endpoints for verification and management
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.models.schemas import (
    OTPRequest, 
    OTPVerification, 
    OTPResponse, 
    MessageResponse
)
from app.models.user import User
from app.models.transaction import Transaction
from app.services.otp_service import OTPService

router = APIRouter()


def get_otp_service(db: Session = Depends(get_db)) -> OTPService:
    """
    Get OTP service instance
    """
    return OTPService(db)


@router.post("/send", response_model=OTPResponse)
async def send_otp(
    otp_request: OTPRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    otp_service: OTPService = Depends(get_otp_service),
    db: Session = Depends(get_db)
):
    """
    Send OTP to user
    """
    # Get transaction if specified
    transaction = None
    if otp_request.transaction_id:
        transaction = db.query(Transaction).filter(
            Transaction.id == otp_request.transaction_id,
            Transaction.sender_id == current_user.id
        ).first()
        
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found"
            )
    
    # Get client IP and user agent
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent")
    
    try:
        otp_record = otp_service.create_otp(
            user=current_user,
            otp_type=otp_request.otp_type,
            delivery_method=otp_request.delivery_method,
            transaction=transaction,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        return otp_record
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send OTP"
        )


@router.post("/verify", response_model=MessageResponse)
async def verify_otp(
    otp_verification: OTPVerification,
    current_user: User = Depends(get_current_user),
    otp_service: OTPService = Depends(get_otp_service),
    db: Session = Depends(get_db)
):
    """
    Verify OTP code
    """
    # Get transaction if specified
    transaction = None
    if otp_verification.transaction_id:
        transaction = db.query(Transaction).filter(
            Transaction.id == otp_verification.transaction_id,
            Transaction.sender_id == current_user.id
        ).first()
        
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found"
            )
    
    try:
        is_valid = otp_service.verify_otp(
            user=current_user,
            otp_code=otp_verification.otp_code,
            otp_type=otp_verification.otp_type,
            transaction=transaction
        )
        
        if is_valid:
            # Update transaction OTP status if applicable
            if transaction:
                transaction.otp_verified = True
                db.commit()
            
            return MessageResponse(message="OTP verified successfully")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid OTP code"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify OTP"
        )


@router.get("/status", response_model=OTPResponse)
async def get_otp_status(
    otp_type: str,
    transaction_id: int = None,
    current_user: User = Depends(get_current_user),
    otp_service: OTPService = Depends(get_otp_service),
    db: Session = Depends(get_db)
):
    """
    Get current OTP status
    """
    # Get transaction if specified
    transaction = None
    if transaction_id:
        transaction = db.query(Transaction).filter(
            Transaction.id == transaction_id,
            Transaction.sender_id == current_user.id
        ).first()
        
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found"
            )
    
    try:
        from app.models.otp import OTPType
        otp_type_enum = OTPType(otp_type)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid OTP type"
        )
    
    otp_record = otp_service.get_otp_status(
        user=current_user,
        otp_type=otp_type_enum,
        transaction=transaction
    )
    
    if not otp_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No OTP record found"
        )
    
    return otp_record


@router.post("/resend", response_model=OTPResponse)
async def resend_otp(
    otp_request: OTPRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    otp_service: OTPService = Depends(get_otp_service),
    db: Session = Depends(get_db)
):
    """
    Resend OTP (same as send OTP but with different endpoint for clarity)
    """
    return await send_otp(otp_request, request, current_user, otp_service, db)
