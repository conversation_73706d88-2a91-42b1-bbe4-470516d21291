"""
Authentication endpoints for user registration, login, and token management
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.dependencies import get_current_user, get_auth_service
from app.models.schemas import (
    UserCreate, 
    UserResponse, 
    LoginRequest, 
    Token, 
    MessageResponse
)
from app.models.user import User
from app.services.auth_service import AuthService

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user account
    """
    try:
        user = auth_service.create_user(user_data)
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )


@router.post("/login", response_model=Token)
async def login_user(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Authenticate user and return JWT tokens
    """
    user = auth_service.authenticate_user(login_data)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Account is deactivated"
        )
    
    # Create tokens
    tokens = auth_service.create_tokens(user)
    return tokens


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information
    """
    return current_user


@router.post("/refresh", response_model=Token)
async def refresh_token(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Refresh access token using refresh token
    """
    tokens = auth_service.create_tokens(current_user)
    return tokens


@router.post("/logout", response_model=MessageResponse)
async def logout_user(
    current_user: User = Depends(get_current_user)
):
    """
    Logout user (client should discard tokens)
    """
    # In a production environment, you might want to blacklist the token
    # For now, we'll just return a success message
    return MessageResponse(message="Successfully logged out")


@router.post("/verify-email", response_model=MessageResponse)
async def verify_email(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Verify user email (placeholder - would integrate with email service)
    """
    # This would typically involve sending an email with verification link
    # For now, we'll mark as verified
    auth_service.update_user_verification_status(
        current_user.id, 
        email_verified=True
    )
    
    return MessageResponse(message="Email verified successfully")


@router.post("/verify-phone", response_model=MessageResponse)
async def verify_phone(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Verify user phone number (placeholder - would integrate with SMS service)
    """
    # This would typically involve sending SMS with verification code
    # For now, we'll mark as verified
    auth_service.update_user_verification_status(
        current_user.id, 
        phone_verified=True
    )
    
    return MessageResponse(message="Phone number verified successfully")
