# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Redis removed - using in-memory caching instead

# Face Recognition and Computer Vision
opencv-python==********
face-recognition==1.3.0
Pillow==10.1.0
numpy==1.24.3

# Vector Database for Face Embeddings
chromadb==0.4.18
sentence-transformers==2.2.2

# Image Processing
scikit-image==0.22.0

# OTP and SMS
twilio==8.10.0
sendgrid==6.10.0

# Environment and Configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2

# Validation and Utilities
email-validator==2.1.0
python-dateutil==2.8.2

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# CORS (handled by FastAPI built-in middleware)
