@echo off
REM Windows setup script for UPI Transaction App
echo 🚀 UPI Transaction App Setup (Windows)
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org/
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js 16+ from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Create backend virtual environment
echo.
echo 🔧 Setting up Backend Virtual Environment...
cd backend
if exist venv (
    echo Virtual environment already exists, removing...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment created

REM Activate virtual environment and install dependencies
echo Installing Python dependencies...
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)

echo ✅ Python dependencies installed
deactivate
cd ..

REM Setup frontend
echo.
echo 🔧 Setting up Frontend Dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo ❌ Failed to install Node.js dependencies
    pause
    exit /b 1
)

echo ✅ Node.js dependencies installed
cd ..

REM Create environment files
echo.
echo 🔧 Setting up Environment Files...
if exist backend\.env.example (
    if not exist backend\.env (
        copy backend\.env.example backend\.env
        echo ✅ Created backend\.env from example
    )
)

if exist frontend\.env.example (
    if not exist frontend\.env (
        copy frontend\.env.example frontend\.env
        echo ✅ Created frontend\.env from example
    )
)

REM Create activation script
echo.
echo 🔧 Creating Activation Script...
(
echo @echo off
echo echo Activating UPI Transaction App Backend Environment...
echo call backend\venv\Scripts\activate.bat
echo echo ✅ Backend virtual environment activated!
echo echo.
echo echo Available commands:
echo echo   cd backend ^&^& uvicorn app.main:app --reload  ^(Start backend server^)
echo echo   cd frontend ^&^& npm run dev                    ^(Start frontend server^)
echo echo   deactivate                                     ^(Deactivate environment^)
echo echo.
echo cmd /k
) > activate.bat

echo ✅ Created activate.bat

REM Print completion message
echo.
echo ============================================================
echo 🎉 SETUP COMPLETE!
echo ============================================================
echo.
echo 📋 Next Steps:
echo 1. Configure your environment variables:
echo    - Edit backend\.env with your database and service credentials
echo    - Edit frontend\.env if needed
echo.
echo 2. Set up your databases:
echo    - Install and start PostgreSQL
echo    - Install and start Redis
echo    - Create database: upi_transaction_db
echo.
echo 3. Start the application:
echo    Backend:
echo      activate.bat
echo      cd backend
echo      uvicorn app.main:app --reload
echo.
echo    Frontend ^(in new terminal^):
echo      cd frontend
echo      npm run dev
echo.
echo 4. Access the application:
echo    - Frontend: http://localhost:3000
echo    - Backend API: http://localhost:8000
echo    - API Docs: http://localhost:8000/api/v1/docs
echo.
echo 🐳 Alternative: Use Docker
echo    docker-compose up -d
echo.
echo ============================================================

pause
