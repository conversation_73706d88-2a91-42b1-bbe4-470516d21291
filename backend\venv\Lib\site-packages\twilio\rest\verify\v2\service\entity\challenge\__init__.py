r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Verify
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.verify.v2.service.entity.challenge.notification import NotificationList


class ChallengeInstance(InstanceResource):
    class ChallengeReasons(object):
        NONE = "none"
        NOT_NEEDED = "not_needed"
        NOT_REQUESTED = "not_requested"

    class ChallengeStatuses(object):
        PENDING = "pending"
        EXPIRED = "expired"
        APPROVED = "approved"
        DENIED = "denied"

    class FactorTypes(object):
        PUSH = "push"
        TOTP = "totp"

    class ListOrders(object):
        ASC = "asc"
        DESC = "desc"

    """
    :ivar sid: A 34 character string that uniquely identifies this Challenge.
    :ivar account_sid: The unique SID identifier of the Account.
    :ivar service_sid: The unique SID identifier of the Service.
    :ivar entity_sid: The unique SID identifier of the Entity.
    :ivar identity: Customer unique identity for the Entity owner of the Challenge. This identifier should be immutable, not PII, length between 8 and 64 characters, and generated by your external system, such as your user's UUID, GUID, or SID. It can only contain dash (-) separated alphanumeric characters.
    :ivar factor_sid: The unique SID identifier of the Factor.
    :ivar date_created: The date that this Challenge was created, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date that this Challenge was updated, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_responded: The date that this Challenge was responded, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar expiration_date: The date-time when this Challenge expires, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format. The default value is five (5) minutes after Challenge creation. The max value is sixty (60) minutes after creation.
    :ivar status: 
    :ivar responded_reason: 
    :ivar details: Details provided to give context about the Challenge. Intended to be shown to the end user.
    :ivar hidden_details: Details provided to give context about the Challenge. Intended to be hidden from the end user. It must be a stringified JSON with only strings values eg. `{\"ip\": \"*************\"}`
    :ivar metadata: Custom metadata associated with the challenge. This is added by the Device/SDK directly to allow for the inclusion of device information. It must be a stringified JSON with only strings values eg. `{\"os\": \"Android\"}`. Can be up to 1024 characters in length.
    :ivar factor_type: 
    :ivar url: The URL of this resource.
    :ivar links: Contains a dictionary of URL links to nested resources of this Challenge.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        service_sid: str,
        identity: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.service_sid: Optional[str] = payload.get("service_sid")
        self.entity_sid: Optional[str] = payload.get("entity_sid")
        self.identity: Optional[str] = payload.get("identity")
        self.factor_sid: Optional[str] = payload.get("factor_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.date_responded: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_responded")
        )
        self.expiration_date: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("expiration_date")
        )
        self.status: Optional["ChallengeInstance.ChallengeStatuses"] = payload.get(
            "status"
        )
        self.responded_reason: Optional[
            "ChallengeInstance.ChallengeReasons"
        ] = payload.get("responded_reason")
        self.details: Optional[Dict[str, object]] = payload.get("details")
        self.hidden_details: Optional[Dict[str, object]] = payload.get("hidden_details")
        self.metadata: Optional[Dict[str, object]] = payload.get("metadata")
        self.factor_type: Optional["ChallengeInstance.FactorTypes"] = payload.get(
            "factor_type"
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "service_sid": service_sid,
            "identity": identity,
            "sid": sid or self.sid,
        }
        self._context: Optional[ChallengeContext] = None

    @property
    def _proxy(self) -> "ChallengeContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ChallengeContext for this ChallengeInstance
        """
        if self._context is None:
            self._context = ChallengeContext(
                self._version,
                service_sid=self._solution["service_sid"],
                identity=self._solution["identity"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "ChallengeInstance":
        """
        Fetch the ChallengeInstance


        :returns: The fetched ChallengeInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ChallengeInstance":
        """
        Asynchronous coroutine to fetch the ChallengeInstance


        :returns: The fetched ChallengeInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        auth_payload: Union[str, object] = values.unset,
        metadata: Union[object, object] = values.unset,
    ) -> "ChallengeInstance":
        """
        Update the ChallengeInstance

        :param auth_payload: The optional payload needed to verify the Challenge. E.g., a TOTP would use the numeric code. For `TOTP` this value must be between 3 and 8 characters long. For `Push` this value can be up to 5456 characters in length
        :param metadata: Custom metadata associated with the challenge. This is added by the Device/SDK directly to allow for the inclusion of device information. It must be a stringified JSON with only strings values eg. `{\\\"os\\\": \\\"Android\\\"}`. Can be up to 1024 characters in length.

        :returns: The updated ChallengeInstance
        """
        return self._proxy.update(
            auth_payload=auth_payload,
            metadata=metadata,
        )

    async def update_async(
        self,
        auth_payload: Union[str, object] = values.unset,
        metadata: Union[object, object] = values.unset,
    ) -> "ChallengeInstance":
        """
        Asynchronous coroutine to update the ChallengeInstance

        :param auth_payload: The optional payload needed to verify the Challenge. E.g., a TOTP would use the numeric code. For `TOTP` this value must be between 3 and 8 characters long. For `Push` this value can be up to 5456 characters in length
        :param metadata: Custom metadata associated with the challenge. This is added by the Device/SDK directly to allow for the inclusion of device information. It must be a stringified JSON with only strings values eg. `{\\\"os\\\": \\\"Android\\\"}`. Can be up to 1024 characters in length.

        :returns: The updated ChallengeInstance
        """
        return await self._proxy.update_async(
            auth_payload=auth_payload,
            metadata=metadata,
        )

    @property
    def notifications(self) -> NotificationList:
        """
        Access the notifications
        """
        return self._proxy.notifications

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Verify.V2.ChallengeInstance {}>".format(context)


class ChallengeContext(InstanceContext):
    def __init__(self, version: Version, service_sid: str, identity: str, sid: str):
        """
        Initialize the ChallengeContext

        :param version: Version that contains the resource
        :param service_sid: The unique SID identifier of the Service.
        :param identity: Customer unique identity for the Entity owner of the Challenge. This identifier should be immutable, not PII, length between 8 and 64 characters, and generated by your external system, such as your user's UUID, GUID, or SID. It can only contain dash (-) separated alphanumeric characters.
        :param sid: A 34 character string that uniquely identifies this Challenge.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "identity": identity,
            "sid": sid,
        }
        self._uri = (
            "/Services/{service_sid}/Entities/{identity}/Challenges/{sid}".format(
                **self._solution
            )
        )

        self._notifications: Optional[NotificationList] = None

    def fetch(self) -> ChallengeInstance:
        """
        Fetch the ChallengeInstance


        :returns: The fetched ChallengeInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ChallengeInstance:
        """
        Asynchronous coroutine to fetch the ChallengeInstance


        :returns: The fetched ChallengeInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        auth_payload: Union[str, object] = values.unset,
        metadata: Union[object, object] = values.unset,
    ) -> ChallengeInstance:
        """
        Update the ChallengeInstance

        :param auth_payload: The optional payload needed to verify the Challenge. E.g., a TOTP would use the numeric code. For `TOTP` this value must be between 3 and 8 characters long. For `Push` this value can be up to 5456 characters in length
        :param metadata: Custom metadata associated with the challenge. This is added by the Device/SDK directly to allow for the inclusion of device information. It must be a stringified JSON with only strings values eg. `{\\\"os\\\": \\\"Android\\\"}`. Can be up to 1024 characters in length.

        :returns: The updated ChallengeInstance
        """
        data = values.of(
            {
                "AuthPayload": auth_payload,
                "Metadata": serialize.object(metadata),
            }
        )

        payload = self._version.update(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        auth_payload: Union[str, object] = values.unset,
        metadata: Union[object, object] = values.unset,
    ) -> ChallengeInstance:
        """
        Asynchronous coroutine to update the ChallengeInstance

        :param auth_payload: The optional payload needed to verify the Challenge. E.g., a TOTP would use the numeric code. For `TOTP` this value must be between 3 and 8 characters long. For `Push` this value can be up to 5456 characters in length
        :param metadata: Custom metadata associated with the challenge. This is added by the Device/SDK directly to allow for the inclusion of device information. It must be a stringified JSON with only strings values eg. `{\\\"os\\\": \\\"Android\\\"}`. Can be up to 1024 characters in length.

        :returns: The updated ChallengeInstance
        """
        data = values.of(
            {
                "AuthPayload": auth_payload,
                "Metadata": serialize.object(metadata),
            }
        )

        payload = await self._version.update_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
            sid=self._solution["sid"],
        )

    @property
    def notifications(self) -> NotificationList:
        """
        Access the notifications
        """
        if self._notifications is None:
            self._notifications = NotificationList(
                self._version,
                self._solution["service_sid"],
                self._solution["identity"],
                self._solution["sid"],
            )
        return self._notifications

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Verify.V2.ChallengeContext {}>".format(context)


class ChallengePage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> ChallengeInstance:
        """
        Build an instance of ChallengeInstance

        :param payload: Payload response from the API
        """
        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Verify.V2.ChallengePage>"


class ChallengeList(ListResource):
    def __init__(self, version: Version, service_sid: str, identity: str):
        """
        Initialize the ChallengeList

        :param version: Version that contains the resource
        :param service_sid: The unique SID identifier of the Service.
        :param identity: Customer unique identity for the Entity owner of the Challenge. This identifier should be immutable, not PII, length between 8 and 64 characters, and generated by your external system, such as your user's UUID, GUID, or SID. It can only contain dash (-) separated alphanumeric characters.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "identity": identity,
        }
        self._uri = "/Services/{service_sid}/Entities/{identity}/Challenges".format(
            **self._solution
        )

    def create(
        self,
        factor_sid: str,
        expiration_date: Union[datetime, object] = values.unset,
        details_message: Union[str, object] = values.unset,
        details_fields: Union[List[object], object] = values.unset,
        hidden_details: Union[object, object] = values.unset,
        auth_payload: Union[str, object] = values.unset,
    ) -> ChallengeInstance:
        """
        Create the ChallengeInstance

        :param factor_sid: The unique SID identifier of the Factor.
        :param expiration_date: The date-time when this Challenge expires, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format. The default value is five (5) minutes after Challenge creation. The max value is sixty (60) minutes after creation.
        :param details_message: Shown to the user when the push notification arrives. Required when `factor_type` is `push`. Can be up to 256 characters in length
        :param details_fields: A list of objects that describe the Fields included in the Challenge. Each object contains the label and value of the field, the label can be up to 36 characters in length and the value can be up to 128 characters in length. Used when `factor_type` is `push`. There can be up to 20 details fields.
        :param hidden_details: Details provided to give context about the Challenge. Not shown to the end user. It must be a stringified JSON with only strings values eg. `{\\\"ip\\\": \\\"*************\\\"}`. Can be up to 1024 characters in length
        :param auth_payload: Optional payload used to verify the Challenge upon creation. Only used with a Factor of type `totp` to carry the TOTP code that needs to be verified. For `TOTP` this value must be between 3 and 8 characters long.

        :returns: The created ChallengeInstance
        """
        data = values.of(
            {
                "FactorSid": factor_sid,
                "ExpirationDate": serialize.iso8601_datetime(expiration_date),
                "Details.Message": details_message,
                "Details.Fields": serialize.map(
                    details_fields, lambda e: serialize.object(e)
                ),
                "HiddenDetails": serialize.object(hidden_details),
                "AuthPayload": auth_payload,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
        )

    async def create_async(
        self,
        factor_sid: str,
        expiration_date: Union[datetime, object] = values.unset,
        details_message: Union[str, object] = values.unset,
        details_fields: Union[List[object], object] = values.unset,
        hidden_details: Union[object, object] = values.unset,
        auth_payload: Union[str, object] = values.unset,
    ) -> ChallengeInstance:
        """
        Asynchronously create the ChallengeInstance

        :param factor_sid: The unique SID identifier of the Factor.
        :param expiration_date: The date-time when this Challenge expires, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format. The default value is five (5) minutes after Challenge creation. The max value is sixty (60) minutes after creation.
        :param details_message: Shown to the user when the push notification arrives. Required when `factor_type` is `push`. Can be up to 256 characters in length
        :param details_fields: A list of objects that describe the Fields included in the Challenge. Each object contains the label and value of the field, the label can be up to 36 characters in length and the value can be up to 128 characters in length. Used when `factor_type` is `push`. There can be up to 20 details fields.
        :param hidden_details: Details provided to give context about the Challenge. Not shown to the end user. It must be a stringified JSON with only strings values eg. `{\\\"ip\\\": \\\"*************\\\"}`. Can be up to 1024 characters in length
        :param auth_payload: Optional payload used to verify the Challenge upon creation. Only used with a Factor of type `totp` to carry the TOTP code that needs to be verified. For `TOTP` this value must be between 3 and 8 characters long.

        :returns: The created ChallengeInstance
        """
        data = values.of(
            {
                "FactorSid": factor_sid,
                "ExpirationDate": serialize.iso8601_datetime(expiration_date),
                "Details.Message": details_message,
                "Details.Fields": serialize.map(
                    details_fields, lambda e: serialize.object(e)
                ),
                "HiddenDetails": serialize.object(hidden_details),
                "AuthPayload": auth_payload,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return ChallengeInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
        )

    def stream(
        self,
        factor_sid: Union[str, object] = values.unset,
        status: Union["ChallengeInstance.ChallengeStatuses", object] = values.unset,
        order: Union["ChallengeInstance.ListOrders", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ChallengeInstance]:
        """
        Streams ChallengeInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str factor_sid: The unique SID identifier of the Factor.
        :param &quot;ChallengeInstance.ChallengeStatuses&quot; status: The Status of the Challenges to fetch. One of `pending`, `expired`, `approved` or `denied`.
        :param &quot;ChallengeInstance.ListOrders&quot; order: The desired sort order of the Challenges list. One of `asc` or `desc` for ascending and descending respectively. Defaults to `asc`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            factor_sid=factor_sid,
            status=status,
            order=order,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        factor_sid: Union[str, object] = values.unset,
        status: Union["ChallengeInstance.ChallengeStatuses", object] = values.unset,
        order: Union["ChallengeInstance.ListOrders", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ChallengeInstance]:
        """
        Asynchronously streams ChallengeInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str factor_sid: The unique SID identifier of the Factor.
        :param &quot;ChallengeInstance.ChallengeStatuses&quot; status: The Status of the Challenges to fetch. One of `pending`, `expired`, `approved` or `denied`.
        :param &quot;ChallengeInstance.ListOrders&quot; order: The desired sort order of the Challenges list. One of `asc` or `desc` for ascending and descending respectively. Defaults to `asc`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            factor_sid=factor_sid,
            status=status,
            order=order,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        factor_sid: Union[str, object] = values.unset,
        status: Union["ChallengeInstance.ChallengeStatuses", object] = values.unset,
        order: Union["ChallengeInstance.ListOrders", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ChallengeInstance]:
        """
        Lists ChallengeInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str factor_sid: The unique SID identifier of the Factor.
        :param &quot;ChallengeInstance.ChallengeStatuses&quot; status: The Status of the Challenges to fetch. One of `pending`, `expired`, `approved` or `denied`.
        :param &quot;ChallengeInstance.ListOrders&quot; order: The desired sort order of the Challenges list. One of `asc` or `desc` for ascending and descending respectively. Defaults to `asc`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                factor_sid=factor_sid,
                status=status,
                order=order,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        factor_sid: Union[str, object] = values.unset,
        status: Union["ChallengeInstance.ChallengeStatuses", object] = values.unset,
        order: Union["ChallengeInstance.ListOrders", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ChallengeInstance]:
        """
        Asynchronously lists ChallengeInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str factor_sid: The unique SID identifier of the Factor.
        :param &quot;ChallengeInstance.ChallengeStatuses&quot; status: The Status of the Challenges to fetch. One of `pending`, `expired`, `approved` or `denied`.
        :param &quot;ChallengeInstance.ListOrders&quot; order: The desired sort order of the Challenges list. One of `asc` or `desc` for ascending and descending respectively. Defaults to `asc`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                factor_sid=factor_sid,
                status=status,
                order=order,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        factor_sid: Union[str, object] = values.unset,
        status: Union["ChallengeInstance.ChallengeStatuses", object] = values.unset,
        order: Union["ChallengeInstance.ListOrders", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ChallengePage:
        """
        Retrieve a single page of ChallengeInstance records from the API.
        Request is executed immediately

        :param factor_sid: The unique SID identifier of the Factor.
        :param status: The Status of the Challenges to fetch. One of `pending`, `expired`, `approved` or `denied`.
        :param order: The desired sort order of the Challenges list. One of `asc` or `desc` for ascending and descending respectively. Defaults to `asc`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ChallengeInstance
        """
        data = values.of(
            {
                "FactorSid": factor_sid,
                "Status": status,
                "Order": order,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return ChallengePage(self._version, response, self._solution)

    async def page_async(
        self,
        factor_sid: Union[str, object] = values.unset,
        status: Union["ChallengeInstance.ChallengeStatuses", object] = values.unset,
        order: Union["ChallengeInstance.ListOrders", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ChallengePage:
        """
        Asynchronously retrieve a single page of ChallengeInstance records from the API.
        Request is executed immediately

        :param factor_sid: The unique SID identifier of the Factor.
        :param status: The Status of the Challenges to fetch. One of `pending`, `expired`, `approved` or `denied`.
        :param order: The desired sort order of the Challenges list. One of `asc` or `desc` for ascending and descending respectively. Defaults to `asc`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ChallengeInstance
        """
        data = values.of(
            {
                "FactorSid": factor_sid,
                "Status": status,
                "Order": order,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return ChallengePage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ChallengePage:
        """
        Retrieve a specific page of ChallengeInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ChallengeInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ChallengePage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ChallengePage:
        """
        Asynchronously retrieve a specific page of ChallengeInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ChallengeInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ChallengePage(self._version, response, self._solution)

    def get(self, sid: str) -> ChallengeContext:
        """
        Constructs a ChallengeContext

        :param sid: A 34 character string that uniquely identifies this Challenge.
        """
        return ChallengeContext(
            self._version,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
            sid=sid,
        )

    def __call__(self, sid: str) -> ChallengeContext:
        """
        Constructs a ChallengeContext

        :param sid: A 34 character string that uniquely identifies this Challenge.
        """
        return ChallengeContext(
            self._version,
            service_sid=self._solution["service_sid"],
            identity=self._solution["identity"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Verify.V2.ChallengeList>"
