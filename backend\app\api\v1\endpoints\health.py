"""
Health check and system status endpoints
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db, get_redis
from app.core.config import settings

router = APIRouter()


@router.get("/health")
async def health_check():
    """
    Basic health check
    """
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "app_name": settings.APP_NAME
    }


@router.get("/health/detailed")
async def detailed_health_check(
    db: Session = Depends(get_db)
):
    """
    Detailed health check including database and cache connectivity
    """
    health_status = {
        "status": "healthy",
        "version": settings.VERSION,
        "app_name": settings.APP_NAME,
        "components": {}
    }
    
    # Check database connectivity
    try:
        db.execute("SELECT 1")
        health_status["components"]["database"] = {"status": "healthy"}
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # Check cache connectivity (in-memory replacement for Redis)
    try:
        cache_client = get_redis()
        cache_client.ping()
        health_status["components"]["cache"] = {"status": "healthy", "type": "in-memory"}
    except Exception as e:
        health_status["components"]["cache"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "in-memory"
        }
        health_status["status"] = "unhealthy"
    
    # Check configuration
    config_issues = []
    if not settings.SECRET_KEY or settings.SECRET_KEY == "your-super-secret-key-change-this-in-production":
        config_issues.append("SECRET_KEY not properly configured")
    
    if config_issues:
        health_status["components"]["configuration"] = {
            "status": "warning",
            "issues": config_issues
        }
    else:
        health_status["components"]["configuration"] = {"status": "healthy"}
    
    return health_status


@router.get("/info")
async def get_api_info():
    """
    Get API information and configuration
    """
    return {
        "app_name": settings.APP_NAME,
        "version": settings.VERSION,
        "debug_mode": settings.DEBUG,
        "high_value_transaction_limit": settings.HIGH_VALUE_TRANSACTION_LIMIT,
        "daily_transaction_limit": settings.DAILY_TRANSACTION_LIMIT,
        "otp_expire_minutes": settings.OTP_EXPIRE_MINUTES,
        "access_token_expire_minutes": settings.ACCESS_TOKEN_EXPIRE_MINUTES,
        "face_recognition_tolerance": settings.FACE_RECOGNITION_TOLERANCE,
        "cors_origins": settings.BACKEND_CORS_ORIGINS
    }
