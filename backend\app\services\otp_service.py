"""
OTP (One-Time Password) service for transaction verification
"""
import random
import string
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from app.models.otp import OTPRecord, OTPType, OTPDeliveryMethod, OTPStatus
from app.models.user import User
from app.models.transaction import Transaction
from app.core.security import get_password_hash, verify_password
from app.core.config import settings
from app.services.notification_service import NotificationService


class OTPService:
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService()
    
    def generate_otp_code(self, length: int = None) -> str:
        """
        Generate random OTP code
        """
        if length is None:
            length = settings.OTP_LENGTH
        
        return ''.join(random.choices(string.digits, k=length))
    
    def create_otp(
        self, 
        user: User, 
        otp_type: OTPType, 
        delivery_method: OTPDeliveryMethod,
        transaction: Optional[Transaction] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> OTPRecord:
        """
        Create and send OTP
        """
        # Check if there's a recent pending OTP
        recent_otp = self.db.query(OTPRecord).filter(
            OTPRecord.user_id == user.id,
            OTPRecord.otp_type == otp_type,
            OTPRecord.status.in_([OTPStatus.PENDING, OTPStatus.SENT]),
            OTPRecord.expires_at > datetime.utcnow()
        ).first()
        
        if recent_otp:
            # Check if it's too soon to send another OTP (rate limiting)
            time_since_last = datetime.utcnow() - recent_otp.created_at
            if time_since_last.total_seconds() < 60:  # 1 minute cooldown
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Please wait before requesting another OTP"
                )
        
        # Generate OTP code
        otp_code = self.generate_otp_code()
        hashed_otp = get_password_hash(otp_code)
        
        # Set expiry time
        expires_at = datetime.utcnow() + timedelta(minutes=settings.OTP_EXPIRE_MINUTES)
        
        # Determine recipient
        recipient_phone = user.phone_number if delivery_method == OTPDeliveryMethod.SMS else None
        recipient_email = user.email if delivery_method == OTPDeliveryMethod.EMAIL else None
        
        # Create OTP record
        otp_record = OTPRecord(
            otp_code=hashed_otp,
            otp_type=otp_type,
            delivery_method=delivery_method,
            user_id=user.id,
            transaction_id=transaction.id if transaction else None,
            recipient_phone=recipient_phone,
            recipient_email=recipient_email,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(otp_record)
        self.db.commit()
        self.db.refresh(otp_record)
        
        # Send OTP
        try:
            if delivery_method == OTPDeliveryMethod.SMS:
                self.notification_service.send_sms_otp(
                    phone_number=user.phone_number,
                    otp_code=otp_code,
                    otp_type=otp_type
                )
            elif delivery_method == OTPDeliveryMethod.EMAIL:
                self.notification_service.send_email_otp(
                    email=user.email,
                    otp_code=otp_code,
                    otp_type=otp_type,
                    user_name=f"{user.first_name} {user.last_name}"
                )
            
            # Update status to sent
            otp_record.status = OTPStatus.SENT
            otp_record.sent_at = datetime.utcnow()
            self.db.commit()
            
        except Exception as e:
            # Update status to failed
            otp_record.status = OTPStatus.FAILED
            otp_record.failure_reason = str(e)
            self.db.commit()
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send OTP"
            )
        
        return otp_record
    
    def verify_otp(
        self, 
        user: User, 
        otp_code: str, 
        otp_type: OTPType,
        transaction: Optional[Transaction] = None
    ) -> bool:
        """
        Verify OTP code
        """
        # Find the most recent valid OTP
        query = self.db.query(OTPRecord).filter(
            OTPRecord.user_id == user.id,
            OTPRecord.otp_type == otp_type,
            OTPRecord.status == OTPStatus.SENT,
            OTPRecord.expires_at > datetime.utcnow()
        )
        
        if transaction:
            query = query.filter(OTPRecord.transaction_id == transaction.id)
        
        otp_record = query.order_by(OTPRecord.created_at.desc()).first()
        
        if not otp_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid OTP found"
            )
        
        # Check verification attempts
        if otp_record.verification_attempts >= otp_record.max_attempts:
            otp_record.status = OTPStatus.FAILED
            self.db.commit()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum verification attempts exceeded"
            )
        
        # Increment verification attempts
        otp_record.verification_attempts += 1
        
        # Verify OTP code
        if verify_password(otp_code, otp_record.otp_code):
            otp_record.status = OTPStatus.VERIFIED
            otp_record.verified_at = datetime.utcnow()
            self.db.commit()
            return True
        else:
            # Check if max attempts reached
            if otp_record.verification_attempts >= otp_record.max_attempts:
                otp_record.status = OTPStatus.FAILED
            
            self.db.commit()
            return False
    
    def get_otp_status(self, user: User, otp_type: OTPType, transaction: Optional[Transaction] = None) -> Optional[OTPRecord]:
        """
        Get current OTP status for user
        """
        query = self.db.query(OTPRecord).filter(
            OTPRecord.user_id == user.id,
            OTPRecord.otp_type == otp_type
        )
        
        if transaction:
            query = query.filter(OTPRecord.transaction_id == transaction.id)
        
        return query.order_by(OTPRecord.created_at.desc()).first()
    
    def cleanup_expired_otps(self):
        """
        Clean up expired OTP records
        """
        expired_otps = self.db.query(OTPRecord).filter(
            OTPRecord.expires_at < datetime.utcnow(),
            OTPRecord.status.in_([OTPStatus.PENDING, OTPStatus.SENT])
        ).all()
        
        for otp in expired_otps:
            otp.status = OTPStatus.EXPIRED
        
        self.db.commit()
        return len(expired_otps)
