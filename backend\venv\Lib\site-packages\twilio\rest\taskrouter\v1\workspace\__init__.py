r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.taskrouter.v1.workspace.activity import ActivityList
from twilio.rest.taskrouter.v1.workspace.event import EventList
from twilio.rest.taskrouter.v1.workspace.task import TaskList
from twilio.rest.taskrouter.v1.workspace.task_channel import TaskChannelList
from twilio.rest.taskrouter.v1.workspace.task_queue import TaskQueueList
from twilio.rest.taskrouter.v1.workspace.worker import WorkerList
from twilio.rest.taskrouter.v1.workspace.workflow import WorkflowList
from twilio.rest.taskrouter.v1.workspace.workspace_cumulative_statistics import (
    WorkspaceCumulativeStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.workspace_real_time_statistics import (
    WorkspaceRealTimeStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.workspace_statistics import (
    WorkspaceStatisticsList,
)


class WorkspaceInstance(InstanceResource):
    class QueueOrder(object):
        FIFO = "FIFO"
        LIFO = "LIFO"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Workspace resource.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar default_activity_name: The name of the default activity.
    :ivar default_activity_sid: The SID of the Activity that will be used when new Workers are created in the Workspace.
    :ivar event_callback_url: The URL we call when an event occurs. If provided, the Workspace will publish events to this URL, for example, to collect data for reporting. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
    :ivar events_filter: The list of Workspace events for which to call `event_callback_url`. For example, if `EventsFilter=task.created, task.canceled, worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
    :ivar friendly_name: The string that you assigned to describe the Workspace resource. For example `Customer Support` or `2014 Election Campaign`.
    :ivar multi_task_enabled: Whether multi-tasking is enabled. The default is `true`, which enables multi-tasking. Multi-tasking allows Workers to handle multiple Tasks simultaneously. When enabled (`true`), each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking each Worker would only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
    :ivar sid: The unique string that we created to identify the Workspace resource.
    :ivar timeout_activity_name: The name of the timeout activity.
    :ivar timeout_activity_sid: The SID of the Activity that will be assigned to a Worker when a Task reservation times out without a response.
    :ivar prioritize_queue_order: 
    :ivar url: The absolute URL of the Workspace resource.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.default_activity_name: Optional[str] = payload.get("default_activity_name")
        self.default_activity_sid: Optional[str] = payload.get("default_activity_sid")
        self.event_callback_url: Optional[str] = payload.get("event_callback_url")
        self.events_filter: Optional[str] = payload.get("events_filter")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.multi_task_enabled: Optional[bool] = payload.get("multi_task_enabled")
        self.sid: Optional[str] = payload.get("sid")
        self.timeout_activity_name: Optional[str] = payload.get("timeout_activity_name")
        self.timeout_activity_sid: Optional[str] = payload.get("timeout_activity_sid")
        self.prioritize_queue_order: Optional[
            "WorkspaceInstance.QueueOrder"
        ] = payload.get("prioritize_queue_order")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[WorkspaceContext] = None

    @property
    def _proxy(self) -> "WorkspaceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkspaceContext for this WorkspaceInstance
        """
        if self._context is None:
            self._context = WorkspaceContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the WorkspaceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the WorkspaceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "WorkspaceInstance":
        """
        Fetch the WorkspaceInstance


        :returns: The fetched WorkspaceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "WorkspaceInstance":
        """
        Asynchronous coroutine to fetch the WorkspaceInstance


        :returns: The fetched WorkspaceInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        default_activity_sid: Union[str, object] = values.unset,
        event_callback_url: Union[str, object] = values.unset,
        events_filter: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        multi_task_enabled: Union[bool, object] = values.unset,
        timeout_activity_sid: Union[str, object] = values.unset,
        prioritize_queue_order: Union[
            "WorkspaceInstance.QueueOrder", object
        ] = values.unset,
    ) -> "WorkspaceInstance":
        """
        Update the WorkspaceInstance

        :param default_activity_sid: The SID of the Activity that will be used when new Workers are created in the Workspace.
        :param event_callback_url: The URL we should call when an event occurs. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
        :param events_filter: The list of Workspace events for which to call event_callback_url. For example if `EventsFilter=task.created,task.canceled,worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
        :param friendly_name: A descriptive string that you create to describe the Workspace resource. For example: `Sales Call Center` or `Customer Support Team`.
        :param multi_task_enabled: Whether to enable multi-tasking. Can be: `true` to enable multi-tasking, or `false` to disable it. However, all workspaces should be maintained as multi-tasking. There is no default when omitting this parameter. A multi-tasking Workspace can't be updated to single-tasking unless it is not a Flex Project and another (legacy) single-tasking Workspace exists. Multi-tasking allows Workers to handle multiple Tasks simultaneously. In multi-tasking mode, each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking mode (legacy mode), each Worker will only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
        :param timeout_activity_sid: The SID of the Activity that will be assigned to a Worker when a Task reservation times out without a response.
        :param prioritize_queue_order:

        :returns: The updated WorkspaceInstance
        """
        return self._proxy.update(
            default_activity_sid=default_activity_sid,
            event_callback_url=event_callback_url,
            events_filter=events_filter,
            friendly_name=friendly_name,
            multi_task_enabled=multi_task_enabled,
            timeout_activity_sid=timeout_activity_sid,
            prioritize_queue_order=prioritize_queue_order,
        )

    async def update_async(
        self,
        default_activity_sid: Union[str, object] = values.unset,
        event_callback_url: Union[str, object] = values.unset,
        events_filter: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        multi_task_enabled: Union[bool, object] = values.unset,
        timeout_activity_sid: Union[str, object] = values.unset,
        prioritize_queue_order: Union[
            "WorkspaceInstance.QueueOrder", object
        ] = values.unset,
    ) -> "WorkspaceInstance":
        """
        Asynchronous coroutine to update the WorkspaceInstance

        :param default_activity_sid: The SID of the Activity that will be used when new Workers are created in the Workspace.
        :param event_callback_url: The URL we should call when an event occurs. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
        :param events_filter: The list of Workspace events for which to call event_callback_url. For example if `EventsFilter=task.created,task.canceled,worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
        :param friendly_name: A descriptive string that you create to describe the Workspace resource. For example: `Sales Call Center` or `Customer Support Team`.
        :param multi_task_enabled: Whether to enable multi-tasking. Can be: `true` to enable multi-tasking, or `false` to disable it. However, all workspaces should be maintained as multi-tasking. There is no default when omitting this parameter. A multi-tasking Workspace can't be updated to single-tasking unless it is not a Flex Project and another (legacy) single-tasking Workspace exists. Multi-tasking allows Workers to handle multiple Tasks simultaneously. In multi-tasking mode, each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking mode (legacy mode), each Worker will only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
        :param timeout_activity_sid: The SID of the Activity that will be assigned to a Worker when a Task reservation times out without a response.
        :param prioritize_queue_order:

        :returns: The updated WorkspaceInstance
        """
        return await self._proxy.update_async(
            default_activity_sid=default_activity_sid,
            event_callback_url=event_callback_url,
            events_filter=events_filter,
            friendly_name=friendly_name,
            multi_task_enabled=multi_task_enabled,
            timeout_activity_sid=timeout_activity_sid,
            prioritize_queue_order=prioritize_queue_order,
        )

    @property
    def activities(self) -> ActivityList:
        """
        Access the activities
        """
        return self._proxy.activities

    @property
    def events(self) -> EventList:
        """
        Access the events
        """
        return self._proxy.events

    @property
    def tasks(self) -> TaskList:
        """
        Access the tasks
        """
        return self._proxy.tasks

    @property
    def task_channels(self) -> TaskChannelList:
        """
        Access the task_channels
        """
        return self._proxy.task_channels

    @property
    def task_queues(self) -> TaskQueueList:
        """
        Access the task_queues
        """
        return self._proxy.task_queues

    @property
    def workers(self) -> WorkerList:
        """
        Access the workers
        """
        return self._proxy.workers

    @property
    def workflows(self) -> WorkflowList:
        """
        Access the workflows
        """
        return self._proxy.workflows

    @property
    def cumulative_statistics(self) -> WorkspaceCumulativeStatisticsList:
        """
        Access the cumulative_statistics
        """
        return self._proxy.cumulative_statistics

    @property
    def real_time_statistics(self) -> WorkspaceRealTimeStatisticsList:
        """
        Access the real_time_statistics
        """
        return self._proxy.real_time_statistics

    @property
    def statistics(self) -> WorkspaceStatisticsList:
        """
        Access the statistics
        """
        return self._proxy.statistics

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkspaceInstance {}>".format(context)


class WorkspaceContext(InstanceContext):
    def __init__(self, version: Version, sid: str):
        """
        Initialize the WorkspaceContext

        :param version: Version that contains the resource
        :param sid: The SID of the Workspace resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Workspaces/{sid}".format(**self._solution)

        self._activities: Optional[ActivityList] = None
        self._events: Optional[EventList] = None
        self._tasks: Optional[TaskList] = None
        self._task_channels: Optional[TaskChannelList] = None
        self._task_queues: Optional[TaskQueueList] = None
        self._workers: Optional[WorkerList] = None
        self._workflows: Optional[WorkflowList] = None
        self._cumulative_statistics: Optional[WorkspaceCumulativeStatisticsList] = None
        self._real_time_statistics: Optional[WorkspaceRealTimeStatisticsList] = None
        self._statistics: Optional[WorkspaceStatisticsList] = None

    def delete(self) -> bool:
        """
        Deletes the WorkspaceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._version.delete(
            method="DELETE",
            uri=self._uri,
        )

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the WorkspaceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._version.delete_async(
            method="DELETE",
            uri=self._uri,
        )

    def fetch(self) -> WorkspaceInstance:
        """
        Fetch the WorkspaceInstance


        :returns: The fetched WorkspaceInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return WorkspaceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> WorkspaceInstance:
        """
        Asynchronous coroutine to fetch the WorkspaceInstance


        :returns: The fetched WorkspaceInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return WorkspaceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        default_activity_sid: Union[str, object] = values.unset,
        event_callback_url: Union[str, object] = values.unset,
        events_filter: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        multi_task_enabled: Union[bool, object] = values.unset,
        timeout_activity_sid: Union[str, object] = values.unset,
        prioritize_queue_order: Union[
            "WorkspaceInstance.QueueOrder", object
        ] = values.unset,
    ) -> WorkspaceInstance:
        """
        Update the WorkspaceInstance

        :param default_activity_sid: The SID of the Activity that will be used when new Workers are created in the Workspace.
        :param event_callback_url: The URL we should call when an event occurs. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
        :param events_filter: The list of Workspace events for which to call event_callback_url. For example if `EventsFilter=task.created,task.canceled,worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
        :param friendly_name: A descriptive string that you create to describe the Workspace resource. For example: `Sales Call Center` or `Customer Support Team`.
        :param multi_task_enabled: Whether to enable multi-tasking. Can be: `true` to enable multi-tasking, or `false` to disable it. However, all workspaces should be maintained as multi-tasking. There is no default when omitting this parameter. A multi-tasking Workspace can't be updated to single-tasking unless it is not a Flex Project and another (legacy) single-tasking Workspace exists. Multi-tasking allows Workers to handle multiple Tasks simultaneously. In multi-tasking mode, each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking mode (legacy mode), each Worker will only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
        :param timeout_activity_sid: The SID of the Activity that will be assigned to a Worker when a Task reservation times out without a response.
        :param prioritize_queue_order:

        :returns: The updated WorkspaceInstance
        """
        data = values.of(
            {
                "DefaultActivitySid": default_activity_sid,
                "EventCallbackUrl": event_callback_url,
                "EventsFilter": events_filter,
                "FriendlyName": friendly_name,
                "MultiTaskEnabled": multi_task_enabled,
                "TimeoutActivitySid": timeout_activity_sid,
                "PrioritizeQueueOrder": prioritize_queue_order,
            }
        )

        payload = self._version.update(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return WorkspaceInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        default_activity_sid: Union[str, object] = values.unset,
        event_callback_url: Union[str, object] = values.unset,
        events_filter: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        multi_task_enabled: Union[bool, object] = values.unset,
        timeout_activity_sid: Union[str, object] = values.unset,
        prioritize_queue_order: Union[
            "WorkspaceInstance.QueueOrder", object
        ] = values.unset,
    ) -> WorkspaceInstance:
        """
        Asynchronous coroutine to update the WorkspaceInstance

        :param default_activity_sid: The SID of the Activity that will be used when new Workers are created in the Workspace.
        :param event_callback_url: The URL we should call when an event occurs. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
        :param events_filter: The list of Workspace events for which to call event_callback_url. For example if `EventsFilter=task.created,task.canceled,worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
        :param friendly_name: A descriptive string that you create to describe the Workspace resource. For example: `Sales Call Center` or `Customer Support Team`.
        :param multi_task_enabled: Whether to enable multi-tasking. Can be: `true` to enable multi-tasking, or `false` to disable it. However, all workspaces should be maintained as multi-tasking. There is no default when omitting this parameter. A multi-tasking Workspace can't be updated to single-tasking unless it is not a Flex Project and another (legacy) single-tasking Workspace exists. Multi-tasking allows Workers to handle multiple Tasks simultaneously. In multi-tasking mode, each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking mode (legacy mode), each Worker will only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
        :param timeout_activity_sid: The SID of the Activity that will be assigned to a Worker when a Task reservation times out without a response.
        :param prioritize_queue_order:

        :returns: The updated WorkspaceInstance
        """
        data = values.of(
            {
                "DefaultActivitySid": default_activity_sid,
                "EventCallbackUrl": event_callback_url,
                "EventsFilter": events_filter,
                "FriendlyName": friendly_name,
                "MultiTaskEnabled": multi_task_enabled,
                "TimeoutActivitySid": timeout_activity_sid,
                "PrioritizeQueueOrder": prioritize_queue_order,
            }
        )

        payload = await self._version.update_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return WorkspaceInstance(self._version, payload, sid=self._solution["sid"])

    @property
    def activities(self) -> ActivityList:
        """
        Access the activities
        """
        if self._activities is None:
            self._activities = ActivityList(
                self._version,
                self._solution["sid"],
            )
        return self._activities

    @property
    def events(self) -> EventList:
        """
        Access the events
        """
        if self._events is None:
            self._events = EventList(
                self._version,
                self._solution["sid"],
            )
        return self._events

    @property
    def tasks(self) -> TaskList:
        """
        Access the tasks
        """
        if self._tasks is None:
            self._tasks = TaskList(
                self._version,
                self._solution["sid"],
            )
        return self._tasks

    @property
    def task_channels(self) -> TaskChannelList:
        """
        Access the task_channels
        """
        if self._task_channels is None:
            self._task_channels = TaskChannelList(
                self._version,
                self._solution["sid"],
            )
        return self._task_channels

    @property
    def task_queues(self) -> TaskQueueList:
        """
        Access the task_queues
        """
        if self._task_queues is None:
            self._task_queues = TaskQueueList(
                self._version,
                self._solution["sid"],
            )
        return self._task_queues

    @property
    def workers(self) -> WorkerList:
        """
        Access the workers
        """
        if self._workers is None:
            self._workers = WorkerList(
                self._version,
                self._solution["sid"],
            )
        return self._workers

    @property
    def workflows(self) -> WorkflowList:
        """
        Access the workflows
        """
        if self._workflows is None:
            self._workflows = WorkflowList(
                self._version,
                self._solution["sid"],
            )
        return self._workflows

    @property
    def cumulative_statistics(self) -> WorkspaceCumulativeStatisticsList:
        """
        Access the cumulative_statistics
        """
        if self._cumulative_statistics is None:
            self._cumulative_statistics = WorkspaceCumulativeStatisticsList(
                self._version,
                self._solution["sid"],
            )
        return self._cumulative_statistics

    @property
    def real_time_statistics(self) -> WorkspaceRealTimeStatisticsList:
        """
        Access the real_time_statistics
        """
        if self._real_time_statistics is None:
            self._real_time_statistics = WorkspaceRealTimeStatisticsList(
                self._version,
                self._solution["sid"],
            )
        return self._real_time_statistics

    @property
    def statistics(self) -> WorkspaceStatisticsList:
        """
        Access the statistics
        """
        if self._statistics is None:
            self._statistics = WorkspaceStatisticsList(
                self._version,
                self._solution["sid"],
            )
        return self._statistics

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkspaceContext {}>".format(context)


class WorkspacePage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> WorkspaceInstance:
        """
        Build an instance of WorkspaceInstance

        :param payload: Payload response from the API
        """
        return WorkspaceInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkspacePage>"


class WorkspaceList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the WorkspaceList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Workspaces"

    def create(
        self,
        friendly_name: str,
        event_callback_url: Union[str, object] = values.unset,
        events_filter: Union[str, object] = values.unset,
        multi_task_enabled: Union[bool, object] = values.unset,
        template: Union[str, object] = values.unset,
        prioritize_queue_order: Union[
            "WorkspaceInstance.QueueOrder", object
        ] = values.unset,
    ) -> WorkspaceInstance:
        """
        Create the WorkspaceInstance

        :param friendly_name: A descriptive string that you create to describe the Workspace resource. It can be up to 64 characters long. For example: `Customer Support` or `2014 Election Campaign`.
        :param event_callback_url: The URL we should call when an event occurs. If provided, the Workspace will publish events to this URL, for example, to collect data for reporting. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
        :param events_filter: The list of Workspace events for which to call event_callback_url. For example, if `EventsFilter=task.created, task.canceled, worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
        :param multi_task_enabled: Whether to enable multi-tasking. Can be: `true` to enable multi-tasking, or `false` to disable it. However, all workspaces should be created as multi-tasking. The default is `true`. Multi-tasking allows Workers to handle multiple Tasks simultaneously. When enabled (`true`), each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking mode (legacy mode), each Worker will only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
        :param template: An available template name. Can be: `NONE` or `FIFO` and the default is `NONE`. Pre-configures the Workspace with the Workflow and Activities specified in the template. `NONE` will create a Workspace with only a set of default activities. `FIFO` will configure TaskRouter with a set of default activities and a single TaskQueue for first-in, first-out distribution, which can be useful when you are getting started with TaskRouter.
        :param prioritize_queue_order:

        :returns: The created WorkspaceInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "EventCallbackUrl": event_callback_url,
                "EventsFilter": events_filter,
                "MultiTaskEnabled": multi_task_enabled,
                "Template": template,
                "PrioritizeQueueOrder": prioritize_queue_order,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return WorkspaceInstance(self._version, payload)

    async def create_async(
        self,
        friendly_name: str,
        event_callback_url: Union[str, object] = values.unset,
        events_filter: Union[str, object] = values.unset,
        multi_task_enabled: Union[bool, object] = values.unset,
        template: Union[str, object] = values.unset,
        prioritize_queue_order: Union[
            "WorkspaceInstance.QueueOrder", object
        ] = values.unset,
    ) -> WorkspaceInstance:
        """
        Asynchronously create the WorkspaceInstance

        :param friendly_name: A descriptive string that you create to describe the Workspace resource. It can be up to 64 characters long. For example: `Customer Support` or `2014 Election Campaign`.
        :param event_callback_url: The URL we should call when an event occurs. If provided, the Workspace will publish events to this URL, for example, to collect data for reporting. See [Workspace Events](https://www.twilio.com/docs/taskrouter/api/event) for more information. This parameter supports Twilio's [Webhooks (HTTP callbacks) Connection Overrides](https://www.twilio.com/docs/usage/webhooks/webhooks-connection-overrides).
        :param events_filter: The list of Workspace events for which to call event_callback_url. For example, if `EventsFilter=task.created, task.canceled, worker.activity.update`, then TaskRouter will call event_callback_url only when a task is created, canceled, or a Worker activity is updated.
        :param multi_task_enabled: Whether to enable multi-tasking. Can be: `true` to enable multi-tasking, or `false` to disable it. However, all workspaces should be created as multi-tasking. The default is `true`. Multi-tasking allows Workers to handle multiple Tasks simultaneously. When enabled (`true`), each Worker can receive parallel reservations up to the per-channel maximums defined in the Workers section. In single-tasking mode (legacy mode), each Worker will only receive a new reservation when the previous task is completed. Learn more at [Multitasking](https://www.twilio.com/docs/taskrouter/multitasking).
        :param template: An available template name. Can be: `NONE` or `FIFO` and the default is `NONE`. Pre-configures the Workspace with the Workflow and Activities specified in the template. `NONE` will create a Workspace with only a set of default activities. `FIFO` will configure TaskRouter with a set of default activities and a single TaskQueue for first-in, first-out distribution, which can be useful when you are getting started with TaskRouter.
        :param prioritize_queue_order:

        :returns: The created WorkspaceInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "EventCallbackUrl": event_callback_url,
                "EventsFilter": events_filter,
                "MultiTaskEnabled": multi_task_enabled,
                "Template": template,
                "PrioritizeQueueOrder": prioritize_queue_order,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return WorkspaceInstance(self._version, payload)

    def stream(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[WorkspaceInstance]:
        """
        Streams WorkspaceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The `friendly_name` of the Workspace resources to read. For example `Customer Support` or `2014 Election Campaign`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(friendly_name=friendly_name, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[WorkspaceInstance]:
        """
        Asynchronously streams WorkspaceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The `friendly_name` of the Workspace resources to read. For example `Customer Support` or `2014 Election Campaign`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            friendly_name=friendly_name, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[WorkspaceInstance]:
        """
        Lists WorkspaceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The `friendly_name` of the Workspace resources to read. For example `Customer Support` or `2014 Election Campaign`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[WorkspaceInstance]:
        """
        Asynchronously lists WorkspaceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The `friendly_name` of the Workspace resources to read. For example `Customer Support` or `2014 Election Campaign`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> WorkspacePage:
        """
        Retrieve a single page of WorkspaceInstance records from the API.
        Request is executed immediately

        :param friendly_name: The `friendly_name` of the Workspace resources to read. For example `Customer Support` or `2014 Election Campaign`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of WorkspaceInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return WorkspacePage(self._version, response)

    async def page_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> WorkspacePage:
        """
        Asynchronously retrieve a single page of WorkspaceInstance records from the API.
        Request is executed immediately

        :param friendly_name: The `friendly_name` of the Workspace resources to read. For example `Customer Support` or `2014 Election Campaign`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of WorkspaceInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return WorkspacePage(self._version, response)

    def get_page(self, target_url: str) -> WorkspacePage:
        """
        Retrieve a specific page of WorkspaceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of WorkspaceInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return WorkspacePage(self._version, response)

    async def get_page_async(self, target_url: str) -> WorkspacePage:
        """
        Asynchronously retrieve a specific page of WorkspaceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of WorkspaceInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return WorkspacePage(self._version, response)

    def get(self, sid: str) -> WorkspaceContext:
        """
        Constructs a WorkspaceContext

        :param sid: The SID of the Workspace resource to update.
        """
        return WorkspaceContext(self._version, sid=sid)

    def __call__(self, sid: str) -> WorkspaceContext:
        """
        Constructs a WorkspaceContext

        :param sid: The SID of the Workspace resource to update.
        """
        return WorkspaceContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkspaceList>"
