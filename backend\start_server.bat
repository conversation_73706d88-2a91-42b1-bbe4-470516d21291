@echo off
echo ========================================
echo   UPI Transaction App - Backend Server
echo ========================================
echo.

cd /d "%~dp0"

echo Checking virtual environment...
if not exist "venv\Scripts\python.exe" (
    echo ❌ Virtual environment not found!
    echo Please run setup first:
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ Virtual environment found
echo.

echo Starting FastAPI server...
echo.
echo The server will be available at:
echo   🌐 Main API: http://localhost:8000
echo   📚 API Docs: http://localhost:8000/api/v1/docs
echo   ❤️ Health Check: http://localhost:8000/health
echo.
echo Press Ctrl+C to stop the server
echo.

venv\Scripts\python.exe run_server.py

echo.
echo Server stopped.
pause
