"""
API Version 1 Router
"""
from fastapi import APIRouter
from app.api.v1.endpoints import auth, transactions, otp, users, health
# from app.api.v1.endpoints import face_recognition  # Temporarily disabled

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(transactions.router, prefix="/transactions", tags=["transactions"])
api_router.include_router(otp.router, prefix="/otp", tags=["otp"])
# api_router.include_router(face_recognition.router, prefix="/face-recognition", tags=["face-recognition"])  # Temporarily disabled
