"""
API Version 1 Router
"""
from fastapi import APIRouter
from app.api.v1.endpoints import auth, transactions, otp, users, health

# Face recognition temporarily disabled due to dependency issues
# To enable: install CMake, then run: pip install face_recognition
FACE_RECOGNITION_AVAILABLE = False
print("ℹ️ Face recognition disabled - install CMake and face_recognition to enable")

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(transactions.router, prefix="/transactions", tags=["transactions"])
api_router.include_router(otp.router, prefix="/otp", tags=["otp"])
# Include face recognition router only if available
if FACE_RECOGNITION_AVAILABLE:
    api_router.include_router(face_recognition.router, prefix="/face-recognition", tags=["face-recognition"])
