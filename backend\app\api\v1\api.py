"""
API Version 1 Router
"""
from fastapi import APIRouter
from app.api.v1.endpoints import auth, transactions, otp, users, health

# Try to import face_recognition, but don't fail if dependencies are missing
try:
    from app.api.v1.endpoints import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Face recognition not available: {e}")
    FACE_RECOGNITION_AVAILABLE = False

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(transactions.router, prefix="/transactions", tags=["transactions"])
api_router.include_router(otp.router, prefix="/otp", tags=["otp"])
# Include face recognition router only if available
if FACE_RECOGNITION_AVAILABLE:
    api_router.include_router(face_recognition.router, prefix="/face-recognition", tags=["face-recognition"])
