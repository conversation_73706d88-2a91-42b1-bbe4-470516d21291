import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  InputAdornment,
  Stepper,
  Step,
  Step<PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  AccountBalance,
  CurrencyRupee,
  Description,
  Send,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { useMutation } from 'react-query';
import { transactionAPI } from '../services/api';
import { useAuthStore } from '../store/authStore';
import OTPVerification from '../components/transaction/OTPVerification';
import FaceVerification from '../components/transaction/FaceVerification';

const steps = ['Transaction Details', 'OTP Verification', 'Face Verification', 'Complete'];

const TransactionPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [activeStep, setActiveStep] = useState(0);
  const [transaction, setTransaction] = useState(null);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();

  const amount = watch('amount');

  // Create transaction mutation
  const createTransactionMutation = useMutation(transactionAPI.create, {
    onSuccess: (response) => {
      setTransaction(response.data);
      setActiveStep(1); // Move to OTP verification
      toast.success('Transaction created! Please verify with OTP.');
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Failed to create transaction';
      setError(errorMessage);
      toast.error(errorMessage);
    },
  });

  // Process transaction mutation
  const processTransactionMutation = useMutation(transactionAPI.process, {
    onSuccess: (response) => {
      toast.success('Transaction completed successfully!');
      navigate('/history');
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.detail || 'Transaction processing failed';
      setError(errorMessage);
      toast.error(errorMessage);
    },
  });

  const onSubmit = async (data) => {
    setError('');
    createTransactionMutation.mutate({
      ...data,
      amount: parseFloat(data.amount),
      transaction_type: 'send_money',
    });
  };

  const handleOTPVerified = () => {
    if (transaction?.requires_face_verification) {
      setActiveStep(2); // Move to face verification
    } else {
      // Process transaction directly
      processTransactionMutation.mutate(transaction.id);
      setActiveStep(3);
    }
  };

  const handleFaceVerified = () => {
    // Process transaction after face verification
    processTransactionMutation.mutate(transaction.id);
    setActiveStep(3);
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="receiver_upi_id"
              label="Recipient UPI ID"
              placeholder="recipient@bank"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AccountBalance />
                  </InputAdornment>
                ),
              }}
              {...register('receiver_upi_id', {
                required: 'Recipient UPI ID is required',
                pattern: {
                  value: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/,
                  message: 'Invalid UPI ID format',
                },
              })}
              error={!!errors.receiver_upi_id}
              helperText={errors.receiver_upi_id?.message}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="amount"
              label="Amount"
              type="number"
              inputProps={{ min: 1, step: 0.01 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <CurrencyRupee />
                  </InputAdornment>
                ),
              }}
              {...register('amount', {
                required: 'Amount is required',
                min: {
                  value: 1,
                  message: 'Amount must be at least ₹1',
                },
                max: {
                  value: user?.per_transaction_limit || 50000,
                  message: `Amount cannot exceed ₹${user?.per_transaction_limit?.toLocaleString()}`,
                },
              })}
              error={!!errors.amount}
              helperText={errors.amount?.message}
            />

            <TextField
              margin="normal"
              fullWidth
              id="description"
              label="Description (Optional)"
              multiline
              rows={3}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Description />
                  </InputAdornment>
                ),
              }}
              {...register('description')}
            />

            {/* Transaction Summary */}
            {amount && amount > 0 && (
              <Card sx={{ mt: 3, bgcolor: 'grey.50' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Transaction Summary
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Amount:</Typography>
                    <Typography fontWeight="bold">₹{parseFloat(amount).toLocaleString()}</Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Security Level:</Typography>
                    <Typography color={amount > 20000 ? 'warning.main' : 'success.main'}>
                      {amount > 20000 ? 'High (Face + OTP)' : 'Standard (OTP)'}
                    </Typography>
                  </Box>
                  {amount > 20000 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      This transaction requires face verification due to high amount.
                    </Alert>
                  )}
                </CardContent>
              </Card>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, py: 1.5 }}
              disabled={createTransactionMutation.isLoading}
              startIcon={<Send />}
            >
              {createTransactionMutation.isLoading ? 'Creating Transaction...' : 'Continue'}
            </Button>
          </Box>
        );

      case 1:
        return (
          <OTPVerification
            transaction={transaction}
            onVerified={handleOTPVerified}
            onError={setError}
          />
        );

      case 2:
        return (
          <FaceVerification
            transaction={transaction}
            onVerified={handleFaceVerified}
            onError={setError}
          />
        );

      case 3:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h5" color="success.main" gutterBottom>
              Transaction Completed!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Your transaction has been processed successfully.
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/history')}
            >
              View Transaction History
            </Button>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Container component="main" maxWidth="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper elevation={4} sx={{ p: 4 }}>
          <Typography component="h1" variant="h4" gutterBottom align="center">
            Send Money
          </Typography>

          {/* Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Error Alert */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {/* Step Content */}
          {renderStepContent()}

          {/* Back Button */}
          {activeStep > 0 && activeStep < 3 && (
            <Box sx={{ mt: 3 }}>
              <Button
                onClick={() => navigate('/dashboard')}
                variant="outlined"
              >
                Cancel Transaction
              </Button>
            </Box>
          )}
        </Paper>
      </motion.div>
    </Container>
  );
};

export default TransactionPage;
