#!/usr/bin/env python3
"""
Simple script to run the FastAPI server with proper Python path setup.
This script ensures the app module can be found regardless of the current directory.
"""
import sys
import os
from pathlib import Path

def main():
    # Get the backend directory (where this script is located)
    backend_dir = Path(__file__).parent.resolve()

    # Add the backend directory to Python path
    sys.path.insert(0, str(backend_dir))

    # Change to the backend directory
    os.chdir(backend_dir)

    # Import uvicorn after setting up the path
    try:
        import uvicorn
    except ImportError:
        print("Error: uvicorn not found. Please install requirements:")
        print("pip install -r requirements.txt")
        sys.exit(1)

    # Test if we can import the app
    try:
        from app.main import app
        print("✅ App module imported successfully")
    except ImportError as e:
        print(f"❌ Error importing app: {e}")
        sys.exit(1)

    print("🚀 Starting FastAPI server...")
    print(f"📁 Working directory: {backend_dir}")
    print(f"🌐 Server will be available at: http://localhost:8000")
    print(f"📚 API docs will be available at: http://localhost:8000/api/v1/docs")

    # Run the server
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=[str(backend_dir / "app")]
    )

if __name__ == "__main__":
    main()
