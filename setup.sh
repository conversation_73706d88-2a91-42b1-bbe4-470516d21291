#!/bin/bash
# Unix/Linux/Mac setup script for UPI Transaction App

set -e  # Exit on any error

echo "🚀 UPI Transaction App Setup (Unix/Linux/Mac)"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_info() {
    echo -e "${BLUE}🔧${NC} $1"
}

# Check if Python is installed and version is 3.9+
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        echo "Please install Python 3.9+ from https://python.org/"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    major=$(echo $python_version | cut -d. -f1)
    minor=$(echo $python_version | cut -d. -f2)
    
    if [ "$major" -lt 3 ] || ([ "$major" -eq 3 ] && [ "$minor" -lt 9 ]); then
        print_error "Python 3.9 or higher is required. Current: $python_version"
        exit 1
    fi
    
    print_status "Python version: $python_version"
}

# Check if Node.js is installed and version is 16+
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        echo "Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
    
    node_version=$(node --version | sed 's/v//')
    major=$(echo $node_version | cut -d. -f1)
    
    if [ "$major" -lt 16 ]; then
        print_error "Node.js 16 or higher is required. Current: $node_version"
        exit 1
    fi
    
    print_status "Node.js version: $node_version"
}

# Create backend virtual environment
setup_backend() {
    print_info "Setting up Backend Virtual Environment..."
    
    cd backend
    
    # Remove existing virtual environment if it exists
    if [ -d "venv" ]; then
        echo "Virtual environment already exists, removing..."
        rm -rf venv
    fi
    
    # Create virtual environment
    python3 -m venv venv
    print_status "Virtual environment created"
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    echo "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    if [ -f "requirements.txt" ]; then
        echo "Installing Python dependencies..."
        pip install -r requirements.txt
        print_status "Python dependencies installed"
    else
        print_warning "requirements.txt not found"
    fi
    
    deactivate
    cd ..
}

# Setup frontend dependencies
setup_frontend() {
    print_info "Setting up Frontend Dependencies..."
    
    cd frontend
    
    if [ -f "package.json" ]; then
        echo "Installing Node.js dependencies..."
        npm install
        print_status "Node.js dependencies installed"
    else
        print_error "package.json not found in frontend directory"
        exit 1
    fi
    
    cd ..
}

# Create environment files
create_env_files() {
    print_info "Setting up Environment Files..."
    
    # Backend .env
    if [ -f "backend/.env.example" ] && [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_status "Created backend/.env from example"
    fi
    
    # Frontend .env
    if [ -f "frontend/.env.example" ] && [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        print_status "Created frontend/.env from example"
    fi
}

# Create activation script
create_activation_script() {
    print_info "Creating Activation Script..."
    
    cat > activate.sh << 'EOF'
#!/bin/bash
echo "Activating UPI Transaction App Backend Environment..."
source backend/venv/bin/activate
echo "✅ Backend virtual environment activated!"
echo ""
echo "Available commands:"
echo "  cd backend && uvicorn app.main:app --reload  # Start backend server"
echo "  cd frontend && npm run dev                    # Start frontend server"
echo "  deactivate                                    # Deactivate environment"
echo ""
exec "$SHELL"
EOF
    
    chmod +x activate.sh
    print_status "Created activate.sh"
}

# Print completion message
print_completion() {
    echo ""
    echo "============================================================"
    echo "🎉 SETUP COMPLETE!"
    echo "============================================================"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Configure your environment variables:"
    echo "   - Edit backend/.env with your database and service credentials"
    echo "   - Edit frontend/.env if needed"
    echo ""
    echo "2. Set up your databases:"
    echo "   - Install and start PostgreSQL"
    echo "   - Install and start Redis"
    echo "   - Create database: upi_transaction_db"
    echo ""
    echo "3. Start the application:"
    echo "   Backend:"
    echo "     ./activate.sh"
    echo "     cd backend"
    echo "     uvicorn app.main:app --reload"
    echo ""
    echo "   Frontend (in new terminal):"
    echo "     cd frontend"
    echo "     npm run dev"
    echo ""
    echo "4. Access the application:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:8000"
    echo "   - API Docs: http://localhost:8000/api/v1/docs"
    echo ""
    echo "🐳 Alternative: Use Docker"
    echo "   docker-compose up -d"
    echo ""
    echo "============================================================"
}

# Main execution
main() {
    echo "Checking prerequisites..."
    check_python
    check_node
    print_status "Prerequisites check passed"
    echo ""
    
    setup_backend
    echo ""
    
    setup_frontend
    echo ""
    
    create_env_files
    echo ""
    
    create_activation_script
    echo ""
    
    print_completion
}

# Run main function
main
