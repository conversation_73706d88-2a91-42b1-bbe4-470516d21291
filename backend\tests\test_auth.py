"""
Test cases for authentication endpoints
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.core.database import get_db, Base
from app.models.user import User

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

class TestAuthentication:
    """Test authentication endpoints"""
    
    def test_register_user(self):
        """Test user registration"""
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "first_name": "Test",
            "last_name": "User",
            "phone_number": "+**********",
            "upi_id": "test@bank"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["first_name"] == user_data["first_name"]
        assert "id" in data
    
    def test_register_duplicate_email(self):
        """Test registration with duplicate email"""
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "first_name": "Test2",
            "last_name": "User2",
            "phone_number": "+**********",
            "upi_id": "test2@bank"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]
    
    def test_login_success(self):
        """Test successful login"""
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        login_data = {
            "email": "<EMAIL>",
            "password": "WrongPassword"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_get_current_user(self):
        """Test getting current user info"""
        # First login to get token
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123"
        }
        
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]
        
        # Get user info
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
    
    def test_get_current_user_invalid_token(self):
        """Test getting current user with invalid token"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401
    
    def test_password_validation(self):
        """Test password validation"""
        user_data = {
            "email": "<EMAIL>",
            "password": "weak",  # Too weak
            "first_name": "Weak",
            "last_name": "User",
            "phone_number": "+**********",
            "upi_id": "weak@bank"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 422  # Validation error
    
    def test_email_validation(self):
        """Test email validation"""
        user_data = {
            "email": "invalid-email",  # Invalid format
            "password": "TestPassword123",
            "first_name": "Invalid",
            "last_name": "Email",
            "phone_number": "+**********",
            "upi_id": "invalid@bank"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 422  # Validation error
    
    def test_upi_id_validation(self):
        """Test UPI ID validation"""
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "first_name": "UPI",
            "last_name": "User",
            "phone_number": "+**********",
            "upi_id": "invalid-upi-format"  # Invalid UPI format
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 422  # Validation error


class TestHealthEndpoints:
    """Test health check endpoints"""
    
    def test_basic_health_check(self):
        """Test basic health check"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
    
    def test_detailed_health_check(self):
        """Test detailed health check"""
        response = client.get("/api/v1/health/detailed")
        assert response.status_code == 200
        
        data = response.json()
        assert "components" in data
        assert "database" in data["components"]
    
    def test_api_info(self):
        """Test API info endpoint"""
        response = client.get("/api/v1/info")
        assert response.status_code == 200
        
        data = response.json()
        assert "app_name" in data
        assert "version" in data


if __name__ == "__main__":
    pytest.main([__file__])
