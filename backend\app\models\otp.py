"""
OTP (One-Time Password) model for transaction verification
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Foreign<PERSON>ey, Enum, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.core.database import Base


class OTPType(PyEnum):
    TRANSACTION_VERIFICATION = "transaction_verification"
    LOGIN_VERIFICATION = "login_verification"
    PASSWORD_RESET = "password_reset"
    PHONE_VERIFICATION = "phone_verification"
    EMAIL_VERIFICATION = "email_verification"


class OTPDeliveryMethod(PyEnum):
    SMS = "sms"
    EMAIL = "email"
    WHATSAPP = "whatsapp"


class OTPStatus(PyEnum):
    PENDING = "pending"
    SENT = "sent"
    VERIFIED = "verified"
    EXPIRED = "expired"
    FAILED = "failed"
    CANCELLED = "cancelled"


class OTPRecord(Base):
    __tablename__ = "otp_records"

    id = Column(Integer, primary_key=True, index=True)
    
    # OTP Details
    otp_code = Column(String(10), nullable=False)  # Hashed OTP for security
    otp_type = Column(Enum(OTPType), nullable=False)
    delivery_method = Column(Enum(OTPDeliveryMethod), nullable=False)
    
    # Associated Records
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    transaction_id = Column(Integer, ForeignKey("transactions.id"), nullable=True)
    
    # Delivery Information
    recipient_phone = Column(String(15), nullable=True)
    recipient_email = Column(String(255), nullable=True)
    
    # Status and Verification
    status = Column(Enum(OTPStatus), default=OTPStatus.PENDING)
    verification_attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    sent_at = Column(DateTime, nullable=True)
    verified_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=False)
    
    # Additional Information
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    failure_reason = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="otp_records")
    transaction = relationship("Transaction", back_populates="otp_records")
    
    def __repr__(self):
        return f"<OTPRecord(id={self.id}, type={self.otp_type}, status={self.status}, user_id={self.user_id})>"
