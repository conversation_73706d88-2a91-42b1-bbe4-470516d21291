#!/usr/bin/env python3
"""
Requirements checker for UPI Transaction App
This script checks if all required software is installed
"""

import sys
import subprocess
import platform
from pathlib import Path

def run_command(command):
    """Run a command and return success status and output"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=10
        )
        return result.returncode == 0, result.stdout.strip()
    except subprocess.TimeoutExpired:
        return False, "Command timed out"
    except Exception as e:
        return False, str(e)

def check_python():
    """Check Python installation and version"""
    print("🐍 Checking Python...")
    
    # Check if Python is available
    success, output = run_command("python --version")
    if not success:
        success, output = run_command("python3 --version")
        if not success:
            print("❌ Python is not installed or not in PATH")
            print("   Please install Python 3.9+ from https://python.org/")
            return False
    
    # Extract version
    try:
        version_str = output.replace("Python ", "")
        major, minor = map(int, version_str.split(".")[:2])
        
        if major < 3 or (major == 3 and minor < 9):
            print(f"❌ Python 3.9+ required, found {version_str}")
            return False
        
        print(f"✅ Python {version_str}")
        return True
    except Exception:
        print(f"❌ Could not parse Python version: {output}")
        return False

def check_node():
    """Check Node.js installation and version"""
    print("📦 Checking Node.js...")
    
    success, output = run_command("node --version")
    if not success:
        print("❌ Node.js is not installed or not in PATH")
        print("   Please install Node.js 16+ from https://nodejs.org/")
        return False
    
    try:
        version_str = output.replace("v", "")
        major = int(version_str.split(".")[0])
        
        if major < 16:
            print(f"❌ Node.js 16+ required, found {version_str}")
            return False
        
        print(f"✅ Node.js {version_str}")
        return True
    except Exception:
        print(f"❌ Could not parse Node.js version: {output}")
        return False

def check_npm():
    """Check npm installation"""
    print("📦 Checking npm...")
    
    success, output = run_command("npm --version")
    if not success:
        print("❌ npm is not installed or not in PATH")
        return False
    
    print(f"✅ npm {output}")
    return True

def check_git():
    """Check Git installation"""
    print("🔧 Checking Git...")
    
    success, output = run_command("git --version")
    if not success:
        print("⚠️  Git is not installed (optional but recommended)")
        print("   Download from https://git-scm.com/")
        return True  # Not critical
    
    print(f"✅ {output}")
    return True

def check_optional_tools():
    """Check optional tools"""
    print("\n🔍 Checking optional tools...")
    
    # Docker
    success, output = run_command("docker --version")
    if success:
        print(f"✅ {output}")
    else:
        print("⚠️  Docker not found (optional for containerized deployment)")
    
    # PostgreSQL
    success, output = run_command("psql --version")
    if success:
        print(f"✅ {output}")
    else:
        print("⚠️  PostgreSQL not found (optional, SQLite will be used)")
    
    # Redis
    success, output = run_command("redis-server --version")
    if success:
        print(f"✅ {output}")
    else:
        print("⚠️  Redis not found (optional for production)")

def check_project_structure():
    """Check if project files exist"""
    print("\n📁 Checking project structure...")
    
    required_files = [
        "backend/requirements.txt",
        "backend/app/main.py",
        "frontend/package.json",
        "frontend/src/App.jsx",
        "docker-compose.yml"
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_system_info():
    """Display system information"""
    print(f"\n💻 System Information:")
    print(f"   OS: {platform.system()} {platform.release()}")
    print(f"   Architecture: {platform.machine()}")
    print(f"   Python: {sys.version}")

def main():
    """Main function"""
    print("🔍 UPI Transaction App - Requirements Check")
    print("=" * 50)
    
    check_system_info()
    print("\n" + "=" * 50)
    
    # Check required tools
    checks = [
        check_python(),
        check_node(),
        check_npm(),
        check_git(),
    ]
    
    # Check optional tools
    check_optional_tools()
    
    # Check project structure
    structure_ok = check_project_structure()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    
    if all(checks) and structure_ok:
        print("✅ All requirements met! You can proceed with setup.")
        print("\nNext steps:")
        if platform.system() == "Windows":
            print("   Run: setup.bat")
        else:
            print("   Run: ./setup.sh")
        print("   Or:  python setup.py")
    else:
        print("❌ Some requirements are missing. Please install them before proceeding.")
        
        if not all(checks):
            print("\nRequired software missing:")
            if not check_python():
                print("   - Python 3.9+")
            if not check_node():
                print("   - Node.js 16+")
            if not check_npm():
                print("   - npm")
        
        if not structure_ok:
            print("\nProject files missing. Please ensure you have the complete project.")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
