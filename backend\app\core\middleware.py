"""
Security middleware for the application
"""
import time
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.database import get_redis

logger = logging.getLogger(__name__)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Add security headers to all responses
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware
    """
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get client IP
        client_ip = request.client.host
        
        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/api/v1/health"]:
            return await call_next(request)
        
        try:
            cache_client = get_redis()
            key = f"rate_limit:{client_ip}"

            # Get current count
            current_count = cache_client.get(key)

            if current_count is None:
                # First request
                cache_client.setex(key, self.period, 1)
            else:
                current_count = int(current_count)
                if current_count >= self.calls:
                    return JSONResponse(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        content={
                            "detail": "Rate limit exceeded. Too many requests.",
                            "retry_after": self.period
                        }
                    )
                else:
                    cache_client.incr(key)

        except Exception as e:
            # If cache is down, allow the request but log the error
            logger.error(f"Rate limiting error: {e}")
        
        return await call_next(request)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Log all requests for security monitoring
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"from {request.client.host} "
            f"User-Agent: {request.headers.get('user-agent', 'Unknown')}"
        )
        
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Response: {response.status_code} "
            f"Time: {process_time:.3f}s "
            f"Path: {request.url.path}"
        )
        
        # Log suspicious activity
        if response.status_code >= 400:
            logger.warning(
                f"Error response: {response.status_code} "
                f"for {request.method} {request.url.path} "
                f"from {request.client.host}"
            )
        
        return response


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """
    IP whitelist middleware for admin endpoints
    """
    
    def __init__(self, app, whitelist: list = None):
        super().__init__(app)
        self.whitelist = whitelist or ["127.0.0.1", "localhost"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check if this is an admin endpoint
        if request.url.path.startswith("/admin"):
            client_ip = request.client.host
            
            if client_ip not in self.whitelist:
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={"detail": "Access denied from this IP address"}
                )
        
        return await call_next(request)


class RequestSizeMiddleware(BaseHTTPMiddleware):
    """
    Limit request body size to prevent DoS attacks
    """
    
    def __init__(self, app, max_size: int = 10 * 1024 * 1024):  # 10MB default
        super().__init__(app)
        self.max_size = max_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check content length
        content_length = request.headers.get("content-length")
        
        if content_length:
            content_length = int(content_length)
            if content_length > self.max_size:
                return JSONResponse(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    content={"detail": "Request body too large"}
                )
        
        return await call_next(request)


class SQLInjectionProtectionMiddleware(BaseHTTPMiddleware):
    """
    Basic SQL injection protection
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(--|#|/\*|\*/)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check query parameters
        query_string = str(request.url.query)
        
        if self._contains_sql_injection(query_string):
            logger.warning(f"Potential SQL injection attempt from {request.client.host}: {query_string}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"detail": "Invalid request parameters"}
            )
        
        return await call_next(request)
    
    def _contains_sql_injection(self, text: str) -> bool:
        """Check if text contains potential SQL injection patterns"""
        import re
        
        for pattern in self.sql_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        
        return False


class CSRFProtectionMiddleware(BaseHTTPMiddleware):
    """
    CSRF protection for state-changing operations
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.protected_methods = ["POST", "PUT", "DELETE", "PATCH"]
        self.exempt_paths = ["/api/v1/auth/login", "/api/v1/auth/register"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check if this request needs CSRF protection
        if (request.method in self.protected_methods and 
            request.url.path not in self.exempt_paths):
            
            # Check for CSRF token in headers
            csrf_token = request.headers.get("X-CSRF-Token")
            origin = request.headers.get("Origin")
            referer = request.headers.get("Referer")
            
            # For API requests, we rely on CORS and authentication
            # In a full implementation, you'd validate CSRF tokens
            if not origin and not referer:
                logger.warning(f"Request without Origin/Referer from {request.client.host}")
        
        return await call_next(request)
