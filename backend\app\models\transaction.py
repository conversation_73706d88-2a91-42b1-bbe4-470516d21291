"""
Transaction model for UPI payments
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.core.database import Base


class TransactionStatus(PyEnum):
    PENDING = "pending"
    OTP_REQUIRED = "otp_required"
    FACE_VERIFICATION_REQUIRED = "face_verification_required"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class TransactionType(PyEnum):
    SEND_MONEY = "send_money"
    REQUEST_MONEY = "request_money"
    MERCHANT_PAYMENT = "merchant_payment"


class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(String(100), unique=True, index=True, nullable=False)  # UPI transaction ID
    
    # Parties involved
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    receiver_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Nullable for merchant payments
    receiver_upi_id = Column(String(100), nullable=False)  # UPI ID of receiver
    
    # Transaction Details
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default="INR")
    description = Column(Text, nullable=True)
    transaction_type = Column(Enum(TransactionType), nullable=False)
    
    # Status and Processing
    status = Column(Enum(TransactionStatus), default=TransactionStatus.PENDING)
    failure_reason = Column(Text, nullable=True)
    
    # Security Verification
    otp_verified = Column(Boolean, default=False)
    face_verified = Column(Boolean, default=False)
    requires_face_verification = Column(Boolean, default=False)
    
    # UPI Specific
    upi_ref_id = Column(String(100), nullable=True)  # Bank reference ID
    merchant_transaction_id = Column(String(100), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)  # For pending transactions
    
    # Relationships
    sender = relationship("User", foreign_keys=[sender_id], back_populates="transactions_sent")
    receiver = relationship("User", foreign_keys=[receiver_id], back_populates="transactions_received")
    otp_records = relationship("OTPRecord", back_populates="transaction")
    
    def __repr__(self):
        return f"<Transaction(id={self.id}, transaction_id={self.transaction_id}, amount={self.amount}, status={self.status})>"
