"""
Simple Face Recognition API endpoints (without complex dependencies)
"""

from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.core.database import get_db
from app.models.face_recognition import FaceTemplate, FaceRecognitionRecord

router = APIRouter()


@router.get("/status", response_model=Dict[str, Any])
async def get_face_recognition_status(db: Session = Depends(get_db)):
    """
    Get face recognition system status
    """
    try:
        # Count existing templates
        template_count = db.query(FaceTemplate).count()
        record_count = db.query(FaceRecognitionRecord).count()
        
        return {
            "status": "disabled",
            "reason": "Face recognition dependencies not installed",
            "requirements": [
                "Install CMake: winget install Kitware.CMake",
                "Install face_recognition: pip install face_recognition",
                "Restart the server"
            ],
            "database_status": {
                "templates_count": template_count,
                "records_count": record_count,
                "database_ready": True
            },
            "features": {
                "enrollment": False,
                "verification": False,
                "vector_database": False
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking face recognition status: {str(e)}"
        )


@router.get("/requirements")
async def get_installation_requirements():
    """
    Get installation requirements for face recognition
    """
    return {
        "title": "Face Recognition Installation Guide",
        "steps": [
            {
                "step": 1,
                "title": "Install CMake",
                "description": "CMake is required to build the dlib library",
                "commands": [
                    "winget install Kitware.CMake",
                    "# Or download from: https://cmake.org/download/"
                ],
                "notes": "Make sure to add CMake to your PATH during installation"
            },
            {
                "step": 2,
                "title": "Install Face Recognition",
                "description": "Install the face recognition library",
                "commands": [
                    "pip install face_recognition",
                    "pip install opencv-python"
                ],
                "notes": "This may take several minutes to compile dlib"
            },
            {
                "step": 3,
                "title": "Restart Server",
                "description": "Restart the FastAPI server to enable face recognition",
                "commands": [
                    "# Stop the current server (Ctrl+C)",
                    "python run_server.py"
                ],
                "notes": "Face recognition endpoints will be automatically enabled"
            }
        ],
        "alternative": {
            "title": "Alternative: Use Pre-compiled Wheels",
            "description": "Use pre-compiled wheels to avoid compilation",
            "commands": [
                "pip install --upgrade pip",
                "pip install dlib-binary",
                "pip install face_recognition"
            ]
        }
    }


@router.post("/enroll")
async def enroll_face_disabled():
    """
    Face enrollment endpoint (disabled)
    """
    raise HTTPException(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        detail={
            "error": "Face recognition not available",
            "message": "Face recognition dependencies are not installed",
            "solution": "Install CMake and face_recognition library",
            "status_endpoint": "/api/v1/face-recognition/status"
        }
    )


@router.post("/verify")
async def verify_face_disabled():
    """
    Face verification endpoint (disabled)
    """
    raise HTTPException(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        detail={
            "error": "Face recognition not available",
            "message": "Face recognition dependencies are not installed",
            "solution": "Install CMake and face_recognition library",
            "status_endpoint": "/api/v1/face-recognition/status"
        }
    )


@router.get("/templates")
async def list_face_templates(db: Session = Depends(get_db)):
    """
    List existing face templates (database only)
    """
    try:
        templates = db.query(FaceTemplate).all()
        return {
            "count": len(templates),
            "templates": [
                {
                    "id": template.id,
                    "user_id": template.user_id,
                    "created_at": template.created_at,
                    "embedding_id": template.embedding_id,
                    "status": "stored_in_database"
                }
                for template in templates
            ],
            "note": "Face recognition features disabled - only showing database records"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing templates: {str(e)}"
        )
