"""
Notification service for sending SMS and email notifications
"""
import logging
from typing import <PERSON><PERSON>
from twilio.rest import Client as TwilioClient
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
from app.core.config import settings
from app.models.otp import OTPType

logger = logging.getLogger(__name__)


class NotificationService:
    def __init__(self):
        # Initialize Twilio client
        if settings.TWILIO_ACCOUNT_SID and settings.TWILIO_AUTH_TOKEN:
            self.twilio_client = TwilioClient(
                settings.TWILIO_ACCOUNT_SID, 
                settings.TWILIO_AUTH_TOKEN
            )
        else:
            self.twilio_client = None
            logger.warning("Twilio credentials not configured")
        
        # Initialize SendGrid client
        if settings.SENDGRID_API_KEY:
            self.sendgrid_client = SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
        else:
            self.sendgrid_client = None
            logger.warning("SendGrid API key not configured")
    
    def send_sms_otp(self, phone_number: str, otp_code: str, otp_type: OTPType) -> bool:
        """
        Send OTP via SMS using Twilio
        """
        if not self.twilio_client or not settings.TWILIO_PHONE_NUMBER:
            logger.error("Twilio not configured for SMS sending")
            # For development, just log the OTP
            logger.info(f"SMS OTP for {phone_number}: {otp_code}")
            return True
        
        try:
            # Create message based on OTP type
            if otp_type == OTPType.TRANSACTION_VERIFICATION:
                message_body = f"Your UPI transaction verification code is: {otp_code}. Valid for {settings.OTP_EXPIRE_MINUTES} minutes. Do not share this code."
            elif otp_type == OTPType.LOGIN_VERIFICATION:
                message_body = f"Your login verification code is: {otp_code}. Valid for {settings.OTP_EXPIRE_MINUTES} minutes."
            else:
                message_body = f"Your verification code is: {otp_code}. Valid for {settings.OTP_EXPIRE_MINUTES} minutes."
            
            # Send SMS
            message = self.twilio_client.messages.create(
                body=message_body,
                from_=settings.TWILIO_PHONE_NUMBER,
                to=phone_number
            )
            
            logger.info(f"SMS sent successfully. SID: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send SMS: {str(e)}")
            return False
    
    def send_email_otp(
        self, 
        email: str, 
        otp_code: str, 
        otp_type: OTPType, 
        user_name: Optional[str] = None
    ) -> bool:
        """
        Send OTP via email using SendGrid
        """
        if not self.sendgrid_client or not settings.SENDGRID_FROM_EMAIL:
            logger.error("SendGrid not configured for email sending")
            # For development, just log the OTP
            logger.info(f"Email OTP for {email}: {otp_code}")
            return True
        
        try:
            # Create email content based on OTP type
            if otp_type == OTPType.TRANSACTION_VERIFICATION:
                subject = "UPI Transaction Verification Code"
                html_content = f"""
                <html>
                <body>
                    <h2>Transaction Verification</h2>
                    <p>Hello {user_name or 'User'},</p>
                    <p>Your UPI transaction verification code is:</p>
                    <h1 style="color: #007bff; font-size: 32px; letter-spacing: 5px;">{otp_code}</h1>
                    <p>This code is valid for {settings.OTP_EXPIRE_MINUTES} minutes.</p>
                    <p><strong>Do not share this code with anyone.</strong></p>
                    <p>If you didn't request this transaction, please contact support immediately.</p>
                </body>
                </html>
                """
            elif otp_type == OTPType.LOGIN_VERIFICATION:
                subject = "Login Verification Code"
                html_content = f"""
                <html>
                <body>
                    <h2>Login Verification</h2>
                    <p>Hello {user_name or 'User'},</p>
                    <p>Your login verification code is:</p>
                    <h1 style="color: #007bff; font-size: 32px; letter-spacing: 5px;">{otp_code}</h1>
                    <p>This code is valid for {settings.OTP_EXPIRE_MINUTES} minutes.</p>
                </body>
                </html>
                """
            else:
                subject = "Verification Code"
                html_content = f"""
                <html>
                <body>
                    <h2>Verification Code</h2>
                    <p>Hello {user_name or 'User'},</p>
                    <p>Your verification code is:</p>
                    <h1 style="color: #007bff; font-size: 32px; letter-spacing: 5px;">{otp_code}</h1>
                    <p>This code is valid for {settings.OTP_EXPIRE_MINUTES} minutes.</p>
                </body>
                </html>
                """
            
            # Create and send email
            message = Mail(
                from_email=settings.SENDGRID_FROM_EMAIL,
                to_emails=email,
                subject=subject,
                html_content=html_content
            )
            
            response = self.sendgrid_client.send(message)
            logger.info(f"Email sent successfully. Status code: {response.status_code}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False
    
    def send_transaction_notification(
        self, 
        email: str, 
        phone_number: str, 
        transaction_details: dict,
        notification_type: str = "success"
    ) -> bool:
        """
        Send transaction notification via email and SMS
        """
        try:
            # Send email notification
            if notification_type == "success":
                subject = "Transaction Successful"
                message_body = f"Your transaction of ₹{transaction_details['amount']} to {transaction_details['receiver']} was successful."
            else:
                subject = "Transaction Failed"
                message_body = f"Your transaction of ₹{transaction_details['amount']} to {transaction_details['receiver']} failed."
            
            # Send email (simplified for now)
            logger.info(f"Transaction notification email sent to {email}: {message_body}")
            
            # Send SMS (simplified for now)
            logger.info(f"Transaction notification SMS sent to {phone_number}: {message_body}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send transaction notification: {str(e)}")
            return False
